@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-plus-circle me-2"></i>Create New Account
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('accounts.index') }}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-arrow-left me-1"></i>Back to Accounts
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-journal-bookmark me-2"></i>New Account Details
                    </h5>
                </div>

                <form method="POST" action="{{ route('accounts.store') }}">
                    @csrf
                    <div class="card-body">

                        <!-- Error Display -->
                        @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <h6 class="alert-heading">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Please fix the following errors:
                            </h6>
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        @endif

                        <!-- Basic Information -->
                        <h6 class="text-primary mb-3">
                            <i class="bi bi-info-circle me-2"></i>Basic Information
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Account Code <span class="text-danger">*</span></label>
                                <input type="text" name="code" class="form-control @error('code') is-invalid @enderror"
                                       placeholder="e.g., 1000" value="{{ old('code') }}" required>
                                @error('code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Unique account code for identification</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Account Name <span class="text-danger">*</span></label>
                                <input type="text" name="name" class="form-control @error('name') is-invalid @enderror"
                                       placeholder="e.g., Cash in Bank" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Classification -->
                        <h6 class="text-primary mb-3 mt-4">
                            <i class="bi bi-tags me-2"></i>Classification
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Account Type <span class="text-danger">*</span></label>
                                <select name="account_type_id" class="form-select @error('account_type_id') is-invalid @enderror" required>
                                    <option value="">Select Account Type</option>
                                    @foreach($accountTypes as $id => $name)
                                        <option value="{{ $id }}" {{ old('account_type_id') == $id ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('account_type_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Account Category <span class="text-danger">*</span></label>
                                <select name="account_category_id" class="form-select @error('account_category_id') is-invalid @enderror" required>
                                    <option value="">Select Category</option>
                                    @foreach($accountCategories as $id => $name)
                                        <option value="{{ $id }}" {{ old('account_category_id') == $id ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('account_category_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Parent Account</label>
                                <select name="parent_account_id" class="form-select @error('parent_account_id') is-invalid @enderror">
                                    <option value="">No Parent (Top Level)</option>
                                    @foreach($parentAccounts as $id => $name)
                                        <option value="{{ $id }}" {{ old('parent_account_id') == $id ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('parent_account_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Optional: Create as sub-account</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Normal Balance <span class="text-danger">*</span></label>
                                <select name="normal_balance" class="form-select @error('normal_balance') is-invalid @enderror" required>
                                    <option value="">Select Normal Balance</option>
                                    <option value="debit" {{ old('normal_balance') == 'debit' ? 'selected' : '' }}>Debit</option>
                                    <option value="credit" {{ old('normal_balance') == 'credit' ? 'selected' : '' }}>Credit</option>
                                </select>
                                @error('normal_balance')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Assets/Expenses = Debit, Liabilities/Equity/Revenue = Credit</small>
                            </div>
                        </div>

                        <!-- Account Settings -->
                        <h6 class="text-primary mb-3 mt-4">
                            <i class="bi bi-gear me-2"></i>Account Settings
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Currency</label>
                                <select name="currency_id" class="form-select @error('currency_id') is-invalid @enderror">
                                    <option value="">Default Currency</option>
                                    @foreach($currencies as $id => $name)
                                        <option value="{{ $id }}" {{ old('currency_id') == $id ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('currency_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Tax Rate (%)</label>
                                <input type="number" name="tax_rate" class="form-control @error('tax_rate') is-invalid @enderror"
                                       step="0.01" min="0" max="100" placeholder="0.00" value="{{ old('tax_rate', 0) }}">
                                @error('tax_rate')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Default tax rate for this account</small>
                            </div>
                        </div>

                        <!-- Opening Balance -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Opening Balance</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" name="opening_balance" class="form-control @error('opening_balance') is-invalid @enderror"
                                           step="0.01" placeholder="0.00" value="{{ old('opening_balance', 0) }}">
                                </div>
                                @error('opening_balance')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Starting balance for this account</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Opening Balance Date</label>
                                <input type="date" name="opening_balance_date" class="form-control @error('opening_balance_date') is-invalid @enderror"
                                       value="{{ old('opening_balance_date', date('Y-m-d')) }}">
                                @error('opening_balance_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Account Behavior -->
                        <h6 class="text-primary mb-3 mt-4">
                            <i class="bi bi-toggles me-2"></i>Account Behavior
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="is_active" value="1"
                                           {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label">Active Account</label>
                                </div>
                                <small class="form-text text-muted">Only active accounts can be used in transactions</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="allows_manual_entries" value="1"
                                           {{ old('allows_manual_entries', true) ? 'checked' : '' }}>
                                    <label class="form-check-label">Allow Manual Entries</label>
                                </div>
                                <small class="form-text text-muted">Allow manual journal entries to this account</small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="is_system" value="1"
                                           {{ old('is_system') ? 'checked' : '' }}>
                                    <label class="form-check-label">System Account</label>
                                </div>
                                <small class="form-text text-muted">System accounts cannot be deleted</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="requires_reconciliation" value="1"
                                           {{ old('requires_reconciliation') ? 'checked' : '' }}>
                                    <label class="form-check-label">Requires Reconciliation</label>
                                </div>
                                <small class="form-text text-muted">Account requires periodic reconciliation</small>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea name="description" class="form-control @error('description') is-invalid @enderror"
                                      rows="3" placeholder="Account description and purpose">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Notes -->
                        <div class="mb-3">
                            <label class="form-label">Notes</label>
                            <textarea name="notes" class="form-control @error('notes') is-invalid @enderror"
                                      rows="2" placeholder="Additional notes or instructions">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                    </div>

                    <div class="card-footer d-flex justify-content-between">
                        <a href="{{ route('accounts.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>Create Account
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-suggest normal balance based on account type
    const accountTypeSelect = document.querySelector('select[name="account_type_id"]');
    const normalBalanceSelect = document.querySelector('select[name="normal_balance"]');

    if (accountTypeSelect && normalBalanceSelect) {
        accountTypeSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const typeName = selectedOption.text.toLowerCase();

            // Auto-suggest normal balance based on account type
            if (typeName.includes('asset') || typeName.includes('expense')) {
                normalBalanceSelect.value = 'debit';
            } else if (typeName.includes('liability') || typeName.includes('equity') || typeName.includes('revenue') || typeName.includes('income')) {
                normalBalanceSelect.value = 'credit';
            }
        });
    }

    // Auto-generate account code suggestion
    const codeInput = document.querySelector('input[name="code"]');
    const nameInput = document.querySelector('input[name="name"]');

    if (codeInput && nameInput && !codeInput.value) {
        nameInput.addEventListener('blur', function() {
            if (!codeInput.value && this.value) {
                // Simple code generation - you can make this more sophisticated
                const words = this.value.split(' ');
                const initials = words.map(word => word.charAt(0).toUpperCase()).join('');
                const timestamp = Date.now().toString().slice(-4);
                codeInput.placeholder = `Suggested: ${initials}${timestamp}`;
            }
        });
    }
});
</script>
@endpush
