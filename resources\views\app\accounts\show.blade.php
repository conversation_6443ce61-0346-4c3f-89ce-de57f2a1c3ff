@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-journal-bookmark me-2"></i>Account Details
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group" role="group">
                <a href="{{ route('accounts.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Accounts
                </a>
                @can('update', $account)
                <a href="{{ route('accounts.edit', $account) }}" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-pencil me-1"></i>Edit Account
                </a>
                @endcan
                <button type="button" class="btn btn-outline-info btn-sm" onclick="window.print()">
                    <i class="bi bi-printer me-1"></i>Print
                </button>
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <!-- Account Overview -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>{{ $account->code }} - {{ $account->name }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Account Code:</strong></td>
                                    <td>{{ $account->code }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Account Type:</strong></td>
                                    <td>
                                        <span class="badge bg-primary">{{ $account->accountType->name ?? 'N/A' }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Category:</strong></td>
                                    <td>{{ $account->accountCategory->name ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Normal Balance:</strong></td>
                                    <td>
                                        <span class="badge {{ $account->normal_balance == 'debit' ? 'bg-success' : 'bg-danger' }}">
                                            {{ ucfirst($account->normal_balance) }}
                                        </span>
                                    </td>
                                </tr>
                                @if($account->parentAccount)
                                <tr>
                                    <td><strong>Parent Account:</strong></td>
                                    <td>
                                        <a href="{{ route('accounts.show', $account->parentAccount) }}">
                                            {{ $account->parentAccount->code }} - {{ $account->parentAccount->name }}
                                        </a>
                                    </td>
                                </tr>
                                @endif
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @if($account->is_active)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-secondary">Inactive</span>
                                        @endif
                                        
                                        @if($account->is_system)
                                            <span class="badge bg-warning ms-1">System</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Manual Entries:</strong></td>
                                    <td>
                                        @if($account->allows_manual_entries)
                                            <i class="bi bi-check-circle-fill text-success"></i> Allowed
                                        @else
                                            <i class="bi bi-x-circle-fill text-danger"></i> Not Allowed
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Opening Balance:</strong></td>
                                    <td>
                                        @if($account->opening_balance)
                                            ${{ number_format($account->opening_balance, 2) }}
                                            @if($account->opening_balance_date)
                                                <br><small class="text-muted">as of {{ $account->opening_balance_date->format('M d, Y') }}</small>
                                            @endif
                                        @else
                                            $0.00
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>
                                        {{ $account->created_at->format('M d, Y') }}
                                        @if($account->createdBy)
                                            <br><small class="text-muted">by {{ $account->createdBy->name }}</small>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($account->description)
                    <div class="mt-3">
                        <h6>Description:</h6>
                        <p class="text-muted">{{ $account->description }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Direct Payments -->
            @if(isset($payments) && $payments->count() > 0)
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul me-2"></i>Direct Payments
                    </h5>
                    <span class="badge bg-primary">{{ $payments->count() }} payments</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Description</th>
                                    <th>Reference</th>
                                    <th class="text-end">Debt</th>
                                    <th class="text-end">Credit</th>
                                    <th class="text-end">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($payments->take(10) as $payment)
                                <tr>
                                    <td>{{ $payment->payment_date ? $payment->payment_date->format('M d, Y') : $payment->created_at->format('M d, Y') }}</td>
                                    <td>{{ $payment->transaction_description ?: $payment->description ?: 'Payment' }}</td>
                                    <td>{{ $payment->reference_no ?: '-' }}</td>
                                    <td class="text-end">
                                        @if($payment->debt > 0)
                                            <span class="text-success fw-bold">${{ number_format($payment->debt, 2) }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td class="text-end">
                                        @if($payment->credit > 0)
                                            <span class="text-danger fw-bold">${{ number_format($payment->credit, 2) }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td class="text-end">
                                        <span class="fw-bold {{ $payment->amount >= 0 ? 'text-success' : 'text-danger' }}">
                                            ${{ number_format(abs($payment->amount), 2) }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    @if($payments->count() > 10)
                    <div class="text-center mt-3">
                        <a href="{{ route('payments.index') }}?account_id={{ $account->id }}" class="btn btn-outline-primary btn-sm">
                            View All Payments ({{ $payments->count() }} total)
                        </a>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Bank Account Payments -->
            @if(isset($bankAccountPayments) && $bankAccountPayments->count() > 0)
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bank me-2"></i>Bank Account Transactions
                    </h5>
                    <span class="badge bg-info">{{ $bankAccountPayments->count() }} transactions</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Bank Account</th>
                                    <th>Description</th>
                                    <th>Reference</th>
                                    <th class="text-end">Amount</th>
                                    <th class="text-center">Type</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($bankAccountPayments->take(10) as $payment)
                                <tr>
                                    <td>{{ $payment->created_at->format('M d, Y') }}</td>
                                    <td>
                                        @if($payment->paymentable)
                                            {{ $payment->paymentable->name }}
                                        @else
                                            N/A
                                        @endif
                                    </td>
                                    <td>{{ $payment->transaction_description }}</td>
                                    <td>{{ $payment->reference_no ?: '-' }}</td>
                                    <td class="text-end">
                                        <span class="fw-bold {{ $payment->amount >= 0 ? 'text-success' : 'text-danger' }}">
                                            ${{ number_format(abs($payment->amount), 2) }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-{{ $payment->amount >= 0 ? 'success' : 'danger' }}">
                                            {{ $payment->amount >= 0 ? 'Credit' : 'Debit' }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    @if($bankAccountPayments->count() > 10)
                    <div class="text-center mt-3">
                        <a href="{{ route('cashbook.index') }}" class="btn btn-outline-primary btn-sm">
                            View All Transactions ({{ $bankAccountPayments->count() }} total)
                        </a>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Recent Journal Entries -->
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>Recent Journal Entries
                    </h5>
                    @if(Route::has('journal-entries.create'))
                    <a href="{{ route('journal-entries.create') }}?account_id={{ $account->id }}" class="btn btn-primary btn-sm">
                        <i class="bi bi-plus-circle me-1"></i>Add Entry
                    </a>
                    @endif
                </div>
                <div class="card-body">
                    @if($journalEntryLines->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Entry #</th>
                                    <th>Description</th>
                                    <th class="text-end">Debit</th>
                                    <th class="text-end">Credit</th>
                                    <th class="text-center">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($journalEntryLines->take(10) as $line)
                                <tr>
                                    <td>{{ $line->journalEntry->entry_date->format('M d, Y') }}</td>
                                    <td>
                                        @if(Route::has('journal-entries.show'))
                                        <a href="{{ route('journal-entries.show', $line->journalEntry) }}">
                                            {{ $line->journalEntry->entry_number }}
                                        </a>
                                        @else
                                            {{ $line->journalEntry->entry_number }}
                                        @endif
                                    </td>
                                    <td>{{ $line->description ?: $line->journalEntry->description }}</td>
                                    <td class="text-end">
                                        @if($line->debit > 0)
                                            <span class="text-success fw-bold">${{ number_format($line->debit, 2) }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td class="text-end">
                                        @if($line->credit > 0)
                                            <span class="text-danger fw-bold">${{ number_format($line->credit, 2) }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-{{ $line->journalEntry->status == 'posted' ? 'success' : 'warning' }}">
                                            {{ ucfirst($line->journalEntry->status) }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    @if($journalEntryLines->count() > 10)
                    <div class="text-center mt-3">
                        @if(Route::has('journal-entries.index'))
                        <a href="{{ route('journal-entries.index') }}?account_id={{ $account->id }}" class="btn btn-outline-primary btn-sm">
                            View All Entries ({{ $journalEntryLines->count() }} total)
                        </a>
                        @endif
                    </div>
                    @endif
                    @else
                    <div class="text-center py-4">
                        <i class="bi bi-journal-x display-4 text-muted d-block mb-2"></i>
                        <p class="text-muted mb-0">No journal entries found for this account.</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Account Statistics -->
        <div class="col-lg-4">
            <!-- Balance Card -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-calculator me-2"></i>Account Balance
                    </h6>
                </div>
                <div class="card-body text-center">
                    <h2 class="mb-1 {{ $account->balance >= 0 ? 'text-success' : 'text-danger' }}">
                        ${{ number_format(abs($account->balance), 2) }}
                        @if($account->balance < 0) <small>(CR)</small> @endif
                    </h2>
                    <p class="text-muted mb-0">Current Balance</p>
                    
                    <hr class="my-3">
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <h5 class="text-success mb-1">${{ number_format($account->debit, 2) }}</h5>
                            <small class="text-muted">Total Debits</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-danger mb-1">${{ number_format($account->credit, 2) }}</h5>
                            <small class="text-muted">Total Credits</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-lightning me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if(Route::has('journal-entries.create'))
                        <a href="{{ route('journal-entries.create') }}?account_id={{ $account->id }}" class="btn btn-primary btn-sm">
                            <i class="bi bi-plus-circle me-1"></i>Add Journal Entry
                        </a>
                        @endif

                        @can('update', $account)
                        <a href="{{ route('accounts.edit', $account) }}" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-pencil me-1"></i>Edit Account
                        </a>
                        @endcan

                        <button type="button" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-graph-up me-1"></i>View Reports
                        </button>

                        <button type="button" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-download me-1"></i>Export Transactions
                        </button>

                        @if(!$account->is_system)
                        @can('delete', $account)
                        <form method="POST" action="{{ route('accounts.destroy', $account) }}" 
                              onsubmit="return confirm('Are you sure you want to delete this account?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                <i class="bi bi-trash me-1"></i>Delete Account
                            </button>
                        </form>
                        @endcan
                        @endif
                    </div>
                </div>
            </div>

            <!-- Account Info -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Account Information
                    </h6>
                </div>
                <div class="card-body">
                    <small class="text-muted d-block mb-2">
                        <strong>Account ID:</strong> {{ $account->id }}
                    </small>
                    <small class="text-muted d-block mb-2">
                        <strong>Full Name:</strong> {{ $account->full_name }}
                    </small>
                    @if($account->updated_at != $account->created_at)
                    <small class="text-muted d-block mb-2">
                        <strong>Last Updated:</strong> {{ $account->updated_at->format('M d, Y H:i') }}
                        @if($account->updatedBy)
                            by {{ $account->updatedBy->name }}
                        @endif
                    </small>
                    @endif
                    <small class="text-muted d-block">
                        <strong>Entry Count:</strong> {{ $journalEntryLines->count() }} journal entries
                        @if(isset($payments))
                            + {{ $payments->count() }} direct payments
                        @endif
                        @if(isset($bankAccountPayments))
                            + {{ $bankAccountPayments->count() }} bank payments
                        @endif
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

@if(session('success'))
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            {{ session('success') }}
        </div>
    </div>
</div>
@endif

@endsection

@push('styles')
<style>
@media print {
    .btn, .card-header .btn-group {
        display: none !important;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
@endpush
