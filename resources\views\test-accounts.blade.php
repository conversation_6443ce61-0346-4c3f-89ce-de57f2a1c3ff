<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Chart of Accounts - Test Page</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-journal-bookmark me-2"></i>Chart of Accounts - Test Page
                        </h4>
                    </div>
                    <div class="card-body">
                        <p class="lead">Test the comprehensive Chart of Accounts functionality.</p>
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-check-circle me-2"></i>Account Management Features
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item">
                                                <i class="bi bi-list-ul text-primary me-2"></i>
                                                <strong>Advanced Filtering:</strong> Search by type, category, status
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-diagram-3 text-success me-2"></i>
                                                <strong>Hierarchical Structure:</strong> Parent-child account relationships
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-calculator text-info me-2"></i>
                                                <strong>Real-time Balances:</strong> Live debit/credit/net balances
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-tags text-warning me-2"></i>
                                                <strong>Account Classification:</strong> Types, categories, normal balance
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-gear text-secondary me-2"></i>
                                                <strong>Account Settings:</strong> Active/inactive, manual entries, system accounts
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-shield-check text-danger me-2"></i>
                                                <strong>System Protection:</strong> System accounts cannot be deleted
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-diagram-2 me-2"></i>Account Structure & Integration
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <h6 class="text-primary">
                                                <i class="bi bi-building me-1"></i>Account Types
                                            </h6>
                                            <p class="small text-muted mb-0">
                                                <strong>Assets:</strong> Cash, Bank, Inventory, Equipment<br>
                                                <strong>Liabilities:</strong> Accounts Payable, Loans<br>
                                                <strong>Equity:</strong> Owner's Equity, Retained Earnings<br>
                                                <strong>Revenue:</strong> Sales, Service Income<br>
                                                <strong>Expenses:</strong> Operating, Administrative
                                            </p>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <h6 class="text-success">
                                                <i class="bi bi-arrow-left-right me-1"></i>Normal Balance Rules
                                            </h6>
                                            <p class="small text-muted mb-0">
                                                <strong>Debit Normal:</strong> Assets, Expenses<br>
                                                <strong>Credit Normal:</strong> Liabilities, Equity, Revenue
                                            </p>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <h6 class="text-info">
                                                <i class="bi bi-link-45deg me-1"></i>GL Integration
                                            </h6>
                                            <p class="small text-muted mb-0">
                                                Accounts integrate with Journal Entries, Bank Accounts, 
                                                and Cashbook for complete accounting workflow.
                                            </p>
                                        </div>
                                        
                                        <div>
                                            <h6 class="text-warning">
                                                <i class="bi bi-graph-up me-1"></i>Reporting Ready
                                            </h6>
                                            <p class="small text-muted mb-0">
                                                Account balances feed into Trial Balance, 
                                                Income Statement, and Balance Sheet reports.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center mb-4">
                            <a href="{{ route('accounts.index') }}" class="btn btn-primary btn-lg">
                                <i class="bi bi-journal-bookmark me-2"></i>View Chart of Accounts
                            </a>
                            
                            <a href="{{ route('accounts.create') }}" class="btn btn-success btn-lg">
                                <i class="bi bi-plus-circle me-2"></i>Create New Account
                            </a>
                            
                            <a href="{{ route('account-types.index') }}" class="btn btn-outline-secondary btn-lg">
                                <i class="bi bi-tags me-2"></i>Manage Account Types
                            </a>
                            
                            <a href="{{ route('account-categories.index') }}" class="btn btn-outline-secondary btn-lg">
                                <i class="bi bi-collection me-2"></i>Manage Categories
                            </a>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="text-primary">
                                    <i class="bi bi-list-check me-2"></i>Account Testing Steps
                                </h5>
                                <ol class="list-group list-group-numbered">
                                    <li class="list-group-item">
                                        <strong>View Chart of Accounts:</strong> Browse existing accounts with filtering
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Create Account Types:</strong> Set up asset, liability, equity, revenue, expense types
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Create Categories:</strong> Organize accounts into logical categories
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Add New Account:</strong> Create account with proper classification
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Set Parent-Child:</strong> Create sub-accounts under main accounts
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Configure Settings:</strong> Set active status, manual entries, opening balance
                                    </li>
                                    <li class="list-group-item">
                                        <strong>View Account Details:</strong> Check balance, transactions, journal entries
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Edit Account:</strong> Modify account properties and settings
                                    </li>
                                </ol>
                            </div>
                            
                            <div class="col-md-6">
                                <h5 class="text-success">
                                    <i class="bi bi-database me-2"></i>Account Data Structure
                                </h5>
                                <div class="bg-light p-3 rounded">
                                    <code class="small">
                                        <strong>Account Fields:</strong><br>
                                        - code: Unique identifier (e.g., 1000)<br>
                                        - name: Account name (e.g., Cash)<br>
                                        - account_type_id: Link to account type<br>
                                        - account_category_id: Link to category<br>
                                        - parent_account_id: For sub-accounts<br>
                                        - normal_balance: debit/credit<br>
                                        - is_active: Active status<br>
                                        - is_system: System protection<br>
                                        - allows_manual_entries: Entry control<br>
                                        - opening_balance: Starting balance<br>
                                        - description: Account purpose<br><br>
                                        
                                        <strong>Calculated Fields:</strong><br>
                                        - debit: Total debit amount<br>
                                        - credit: Total credit amount<br>
                                        - balance: Net balance<br>
                                        - full_name: Code + Name
                                    </code>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-4">
                            <h6 class="alert-heading">
                                <i class="bi bi-info-circle me-2"></i>Chart of Accounts Benefits
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li><strong>Organized Structure:</strong> Logical account hierarchy</li>
                                        <li><strong>Real-time Balances:</strong> Live financial data</li>
                                        <li><strong>Flexible Classification:</strong> Custom types and categories</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li><strong>System Integration:</strong> Works with all accounting modules</li>
                                        <li><strong>Audit Trail:</strong> Complete transaction history</li>
                                        <li><strong>Reporting Foundation:</strong> Basis for all financial reports</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning mt-3">
                            <h6 class="alert-heading">
                                <i class="bi bi-exclamation-triangle me-2"></i>Best Practices
                            </h6>
                            <p class="mb-0">
                                <strong>Account Codes:</strong> Use consistent numbering (1000-1999 for Assets, 2000-2999 for Liabilities, etc.). 
                                <strong>Account Names:</strong> Use clear, descriptive names. 
                                <strong>System Accounts:</strong> Mark critical accounts as system accounts to prevent deletion.
                                <strong>Parent-Child:</strong> Use hierarchical structure for better organization and reporting.
                            </p>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <i class="bi bi-journal-bookmark display-4 d-block mb-2"></i>
                                        <h5>Chart of Accounts</h5>
                                        <p class="small mb-0">Complete account management system</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <i class="bi bi-diagram-3 display-4 d-block mb-2"></i>
                                        <h5>Hierarchical Structure</h5>
                                        <p class="small mb-0">Parent-child account relationships</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <i class="bi bi-calculator display-4 d-block mb-2"></i>
                                        <h5>Real-time Balances</h5>
                                        <p class="small mb-0">Live debit/credit calculations</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
