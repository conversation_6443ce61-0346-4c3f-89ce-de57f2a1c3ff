<?php

namespace App\Http\Controllers;

use App\Models\Status;
use App\Models\Account;
use App\Models\AccountType;
use App\Models\AccountCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Facades\App\Libraries\InvoiceHandler;

class AccountController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Account::class);

        $search = $request->get('search', '');
        $accountTypeId = $request->get('account_type_id', '');
        $accountCategoryId = $request->get('account_category_id', '');

        $accounts = Account::search($search);

        if ($accountTypeId) {
            $accounts->where('account_type_id', $accountTypeId);
        }

        if ($accountCategoryId) {
            $accounts->where('account_category_id', $accountCategoryId);
        }

        $accounts = $accounts->latest()
            ->paginate()
            ->withQueryString();

        $accountTypes = AccountType::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');

        $accountCategories = AccountCategory::where('is_active', true)
            ->when($accountTypeId, function ($query) use ($accountTypeId) {
                return $query->where('account_type_id', $accountTypeId);
            })
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.accounts.index', compact(
            'accounts',
            'search',
            'accountTypes',
            'accountCategories',
            'accountTypeId',
            'accountCategoryId'
        ));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Account::class);

        $accountTypes = AccountType::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');

        $accountCategories = AccountCategory::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');

        $parentAccounts = Account::where('is_active', true)
            ->orderBy('code')
            ->get()
            ->pluck('full_name', 'id');

        // Get currencies if the model exists
        $currencies = [];
        if (class_exists(\App\Models\Currency::class)) {
            $currencies = \App\Models\Currency::where('is_active', true)
                ->orderBy('name')
                ->pluck('name', 'id');
        }

        return view('app.accounts.create', compact(
            'accountTypes',
            'accountCategories',
            'parentAccounts',
            'currencies'
        ));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', Account::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:255|unique:accounts,code',
            'description' => 'nullable|string',
            'account_type_id' => 'required|tenant_exists:account_types,id',
            'account_category_id' => 'required|tenant_exists:account_categories,id',
            'parent_account_id' => 'nullable|tenant_exists:accounts,id',
            'is_active' => 'boolean',
            'is_system' => 'boolean',
            'allows_manual_entries' => 'boolean',
            'normal_balance' => 'required|in:debit,credit',
            'opening_balance' => 'nullable|numeric',
            'opening_balance_date' => 'nullable|date',
        ]);

        $account = Account::create($validated);

        return redirect()
            ->route('accounts.edit', $account)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Account $account
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Account $account)
    {
        $this->authorize('view', $account);

        // Get direct transactions for this account
        $transactions = $account->payments()
            ->orderBy('created_at', 'desc')
            ->get();

        // Get bank account payments if this account is linked to bank accounts
        $bankAccountPayments = $account->bankAccountPayments()
            ->with(['paymentable', 'currency'])
            ->orderBy('created_at', 'desc')
            ->get();

        $journalEntryLines = $account->journalEntryLines()
            ->with('journalEntry')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('app.accounts.show', compact(
            'account',
            'transactions',
            'bankAccountPayments',
            'journalEntryLines'
        ));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Account $account
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Account $account)
    {
        $this->authorize('update', $account);

        $accountTypes = AccountType::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');

        $accountCategories = AccountCategory::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');

        $parentAccounts = Account::where('is_active', true)
            ->where('id', '!=', $account->id)
            ->orderBy('code')
            ->get()
            ->pluck('full_name', 'id');

        // Get currencies if the model exists
        $currencies = [];
        if (class_exists(\App\Models\Currency::class)) {
            $currencies = \App\Models\Currency::where('is_active', true)
                ->orderBy('name')
                ->pluck('name', 'id');
        }

        return view('app.accounts.edit', compact(
            'account',
            'accountTypes',
            'accountCategories',
            'parentAccounts',
            'currencies'
        ));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Account $account
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Account $account)
    {
        $this->authorize('update', $account);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:255|unique:accounts,code,' . $account->id,
            'description' => 'nullable|string',
            'account_type_id' => 'required|tenant_exists:account_types,id',
            'account_category_id' => 'required|tenant_exists:account_categories,id',
            'parent_account_id' => 'nullable|tenant_exists:accounts,id',
            'is_active' => 'boolean',
            'is_system' => 'boolean',
            'allows_manual_entries' => 'boolean',
            'normal_balance' => 'required|in:debit,credit',
            'opening_balance' => 'nullable|numeric',
            'opening_balance_date' => 'nullable|date',
        ]);

        // Prevent circular references in parent-child relationships
        if ($validated['parent_account_id'] == $account->id) {
            $validated['parent_account_id'] = null;
        }

        $account->update($validated);

        return redirect()
            ->route('accounts.edit', $account)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Account $account
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Account $account)
    {
        $this->authorize('delete', $account);

        if ($account->is_system) {
            return redirect()
                ->route('accounts.index')
                ->withError('System accounts cannot be deleted.');
        }

        if ($account->journalEntryLines()->count() > 0 ||
            $account->transactions()->count() > 0 ||
            $account->bankAccountPayments()->count() > 0) {
            return redirect()
                ->route('accounts.index')
                ->withError('Account has transactions and cannot be deleted.');
        }

        if ($account->childAccounts()->count() > 0) {
            return redirect()
                ->route('accounts.index')
                ->withError('Account has child accounts and cannot be deleted.');
        }

        $account->delete();

        return redirect()
            ->route('accounts.index')
            ->withSuccess(__('crud.common.removed'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function getAccountCategories(Request $request)
    {
        $accountTypeId = $request->get('account_type_id');

        $accountCategories = AccountCategory::where('is_active', true)
            ->where('account_type_id', $accountTypeId)
            ->orderBy('name')
            ->get(['id', 'name']);

        return response()->json($accountCategories);
    }
}
