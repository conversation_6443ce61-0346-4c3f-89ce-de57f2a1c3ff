<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707 = $component; } ?>
<?php $component = App\View\Components\PageHeader::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\PageHeader::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('title', null, []); ?> 
            <i class="bi bi-calculator me-2"></i>Budgets
         <?php $__env->endSlot(); ?>
         <?php $__env->slot('controls', null, []); ?> 
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Budget::class)): ?>
            <div class="btn-group" role="group">
                <a href="<?php echo e(route('budgets.create')); ?>" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>Create Budget
                </a>
                <a href="<?php echo e(route('budgets.report')); ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-graph-up me-1"></i>Budget Report
                </a>
            </div>
            <?php endif; ?>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707)): ?>
<?php $component = $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707; ?>
<?php unset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707); ?>
<?php endif; ?>
    <!-- End Page Header -->

    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-funnel me-2"></i>Filters
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('budgets.index')); ?>">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Search</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="Search budget name..." value="<?php echo e($search); ?>">
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">Fiscal Year</label>
                        <select name="fiscal_year_id" class="form-select">
                            <option value="">All Fiscal Years</option>
                            <?php $__currentLoopData = $fiscalYears; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $id => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($id); ?>" <?php echo e($fiscalYearId == $id ? 'selected' : ''); ?>>
                                    <?php echo e($name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Statuses</option>
                            <option value="draft" <?php echo e($status === 'draft' ? 'selected' : ''); ?>>Draft</option>
                            <option value="approved" <?php echo e($status === 'approved' ? 'selected' : ''); ?>>Approved</option>
                            <option value="active" <?php echo e($status === 'active' ? 'selected' : ''); ?>>Active</option>
                            <option value="closed" <?php echo e($status === 'closed' ? 'selected' : ''); ?>>Closed</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>Filter
                        </button>
                        <a href="<?php echo e(route('budgets.index')); ?>" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Budgets Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>Budget List
            </h5>
            <span class="badge bg-secondary"><?php echo e($budgets->total()); ?> budgets</span>
        </div>
        
        <div class="table-responsive">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>Budget Name</th>
                        <th>Fiscal Year</th>
                        <th>Version</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Active</th>
                        <th>Created</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $budgets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $budget): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td>
                            <div>
                                <strong><?php echo e($budget->name); ?></strong>
                                <?php if($budget->description): ?>
                                    <br><small class="text-muted"><?php echo e(Str::limit($budget->description, 50)); ?></small>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <?php echo e($budget->fiscalYear->name ?? 'N/A'); ?>

                        </td>
                        <td>
                            <?php echo e($budget->version ?? 'v1.0'); ?>

                        </td>
                        <td class="text-center">
                            <?php switch($budget->status):
                                case ('draft'): ?>
                                    <span class="badge bg-secondary">Draft</span>
                                    <?php break; ?>
                                <?php case ('approved'): ?>
                                    <span class="badge bg-success">Approved</span>
                                    <?php break; ?>
                                <?php case ('active'): ?>
                                    <span class="badge bg-primary">Active</span>
                                    <?php break; ?>
                                <?php case ('closed'): ?>
                                    <span class="badge bg-dark">Closed</span>
                                    <?php break; ?>
                                <?php default: ?>
                                    <span class="badge bg-light text-dark"><?php echo e(ucfirst($budget->status)); ?></span>
                            <?php endswitch; ?>
                        </td>
                        <td class="text-center">
                            <?php if($budget->is_active): ?>
                                <i class="bi bi-check-circle-fill text-success" title="Active Budget"></i>
                            <?php else: ?>
                                <i class="bi bi-circle text-muted" title="Inactive"></i>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="d-block"><?php echo e($budget->created_at->format('M d, Y')); ?></span>
                            <small class="text-muted"><?php echo e($budget->created_at->format('H:i')); ?></small>
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $budget)): ?>
                                <a href="<?php echo e(route('budgets.show', $budget)); ?>" 
                                   class="btn btn-sm btn-outline-info" title="View Details">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <?php endif; ?>
                                
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $budget)): ?>
                                <a href="<?php echo e(route('budgets.edit', $budget)); ?>" 
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <?php endif; ?>
                                
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $budget)): ?>
                                <form method="POST" action="<?php echo e(route('budgets.destroy', $budget)); ?>" 
                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this budget?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-calculator display-4 d-block mb-2"></i>
                                <p class="mb-0">No budgets found.</p>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Budget::class)): ?>
                                <a href="<?php echo e(route('budgets.create')); ?>" class="btn btn-primary btn-sm mt-2">
                                    <i class="bi bi-plus-circle me-1"></i>Create First Budget
                                </a>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if($budgets->hasPages()): ?>
        <div class="card-footer">
            <?php echo e($budgets->appends(request()->query())->links()); ?>

        </div>
        <?php endif; ?>
    </div>

    <!-- Summary Cards -->
    <?php if($budgets->count() > 0): ?>
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1"><?php echo e($budgets->where('status', 'active')->count()); ?></h3>
                    <small>Active Budgets</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1"><?php echo e($budgets->where('status', 'approved')->count()); ?></h3>
                    <small>Approved Budgets</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1"><?php echo e($budgets->where('status', 'draft')->count()); ?></h3>
                    <small>Draft Budgets</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-dark text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1"><?php echo e($budgets->where('status', 'closed')->count()); ?></h3>
                    <small>Closed Budgets</small>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php if(session('success')): ?>
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            <?php echo e(session('success')); ?>

        </div>
    </div>
</div>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Acounting\github\accounting\resources\views/app/budgets/index.blade.php ENDPATH**/ ?>