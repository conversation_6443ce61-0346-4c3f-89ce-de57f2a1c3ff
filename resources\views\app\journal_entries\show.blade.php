@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-journal-text me-2"></i>Journal Entry Details
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group" role="group">
                <a href="{{ route('journal-entries.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Journal Entries
                </a>
                @can('update', $journalEntry)
                @if($journalEntry->status === 'draft')
                <a href="{{ route('journal-entries.edit', $journalEntry) }}" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-pencil me-1"></i>Edit Entry
                </a>
                @endif
                @endcan
                <button type="button" class="btn btn-outline-info btn-sm" onclick="window.print()">
                    <i class="bi bi-printer me-1"></i>Print
                </button>
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <!-- Journal Entry Overview -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>{{ $journalEntry->entry_number }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Entry Number:</strong></td>
                                    <td>{{ $journalEntry->entry_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Entry Date:</strong></td>
                                    <td>{{ $journalEntry->entry_date->format('M d, Y') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Reference:</strong></td>
                                    <td>{{ $journalEntry->reference_number ?: 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Entry Type:</strong></td>
                                    <td>
                                        <span class="badge bg-{{ $journalEntry->entry_type === 'manual' ? 'primary' : 'info' }}">
                                            {{ ucfirst($journalEntry->entry_type) }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @switch($journalEntry->status)
                                            @case('draft')
                                                <span class="badge bg-secondary">Draft</span>
                                                @break
                                            @case('approved')
                                                <span class="badge bg-warning">Approved</span>
                                                @break
                                            @case('posted')
                                                <span class="badge bg-success">Posted</span>
                                                @break
                                            @case('reversed')
                                                <span class="badge bg-danger">Reversed</span>
                                                @break
                                        @endswitch
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Fiscal Year:</strong></td>
                                    <td>{{ $journalEntry->fiscalYear->name ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Fiscal Period:</strong></td>
                                    <td>{{ $journalEntry->fiscalPeriod->name ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Currency:</strong></td>
                                    <td>{{ $journalEntry->currency->name ?? 'Default' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($journalEntry->description)
                    <div class="mt-3">
                        <h6>Description:</h6>
                        <p class="text-muted">{{ $journalEntry->description }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Journal Entry Lines -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul me-2"></i>Journal Entry Lines
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Account</th>
                                    <th>Description</th>
                                    <th class="text-end">Debit</th>
                                    <th class="text-end">Credit</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($journalEntryLines as $line)
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ $line->account->code }}</strong>
                                            <br>
                                            <small class="text-muted">{{ $line->account->name }}</small>
                                        </div>
                                    </td>
                                    <td>{{ $line->description ?: '-' }}</td>
                                    <td class="text-end">
                                        @if($line->debit > 0)
                                            <span class="fw-bold text-success">${{ number_format($line->debit, 2) }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td class="text-end">
                                        @if($line->credit > 0)
                                            <span class="fw-bold text-danger">${{ number_format($line->credit, 2) }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="2">Totals:</th>
                                    <th class="text-end">
                                        <span class="fw-bold text-success">
                                            ${{ number_format($journalEntryLines->sum('debit'), 2) }}
                                        </span>
                                    </th>
                                    <th class="text-end">
                                        <span class="fw-bold text-danger">
                                            ${{ number_format($journalEntryLines->sum('credit'), 2) }}
                                        </span>
                                    </th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    
                    @php
                        $totalDebits = $journalEntryLines->sum('debit');
                        $totalCredits = $journalEntryLines->sum('credit');
                        $isBalanced = abs($totalDebits - $totalCredits) < 0.01;
                    @endphp
                    
                    <div class="alert alert-{{ $isBalanced ? 'success' : 'danger' }} mt-3">
                        <i class="bi bi-{{ $isBalanced ? 'check-circle' : 'exclamation-triangle' }} me-2"></i>
                        <strong>Balance Check:</strong> 
                        @if($isBalanced)
                            This journal entry is balanced (debits = credits).
                        @else
                            This journal entry is NOT balanced! Difference: ${{ number_format(abs($totalDebits - $totalCredits), 2) }}
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Journal Entry Actions -->
        <div class="col-lg-4">
            <!-- Status Card -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-gear me-2"></i>Entry Status
                    </h6>
                </div>
                <div class="card-body text-center">
                    @switch($journalEntry->status)
                        @case('draft')
                            <i class="bi bi-file-earmark display-4 text-secondary d-block mb-2"></i>
                            <h5 class="text-secondary">Draft</h5>
                            <p class="text-muted small">Entry is in draft mode and can be edited.</p>
                            @break
                        @case('approved')
                            <i class="bi bi-check-circle display-4 text-warning d-block mb-2"></i>
                            <h5 class="text-warning">Approved</h5>
                            <p class="text-muted small">Entry has been approved and is ready for posting.</p>
                            @break
                        @case('posted')
                            <i class="bi bi-bookmark-check display-4 text-success d-block mb-2"></i>
                            <h5 class="text-success">Posted</h5>
                            <p class="text-muted small">Entry has been posted to the general ledger.</p>
                            @break
                        @case('reversed')
                            <i class="bi bi-arrow-counterclockwise display-4 text-danger d-block mb-2"></i>
                            <h5 class="text-danger">Reversed</h5>
                            <p class="text-muted small">Entry has been reversed.</p>
                            @break
                    @endswitch
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-lightning me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @can('update', $journalEntry)
                        @if($journalEntry->status === 'draft')
                        <a href="{{ route('journal-entries.edit', $journalEntry) }}" class="btn btn-primary btn-sm">
                            <i class="bi bi-pencil me-1"></i>Edit Entry
                        </a>
                        
                        <button type="button" class="btn btn-success btn-sm">
                            <i class="bi bi-check-circle me-1"></i>Approve Entry
                        </button>
                        @endif
                        
                        @if($journalEntry->status === 'approved')
                        <button type="button" class="btn btn-success btn-sm">
                            <i class="bi bi-bookmark-check me-1"></i>Post Entry
                        </button>
                        @endif
                        
                        @if($journalEntry->status === 'posted')
                        <button type="button" class="btn btn-warning btn-sm">
                            <i class="bi bi-arrow-counterclockwise me-1"></i>Reverse Entry
                        </button>
                        @endif
                        @endcan

                        <button type="button" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-download me-1"></i>Export Entry
                        </button>

                        @if($journalEntry->status === 'draft')
                        @can('delete', $journalEntry)
                        <form method="POST" action="{{ route('journal-entries.destroy', $journalEntry) }}" 
                              onsubmit="return confirm('Are you sure you want to delete this journal entry?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                <i class="bi bi-trash me-1"></i>Delete Entry
                            </button>
                        </form>
                        @endcan
                        @endif
                    </div>
                </div>
            </div>

            <!-- Entry Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Entry Information
                    </h6>
                </div>
                <div class="card-body">
                    <small class="text-muted d-block mb-2">
                        <strong>Entry ID:</strong> {{ $journalEntry->id }}
                    </small>
                    <small class="text-muted d-block mb-2">
                        <strong>Created:</strong> {{ $journalEntry->created_at->format('M d, Y H:i') }}
                        @if($journalEntry->createdBy)
                            by {{ $journalEntry->createdBy->name }}
                        @endif
                    </small>
                    @if($journalEntry->updated_at != $journalEntry->created_at)
                    <small class="text-muted d-block mb-2">
                        <strong>Last Updated:</strong> {{ $journalEntry->updated_at->format('M d, Y H:i') }}
                        @if($journalEntry->updatedBy)
                            by {{ $journalEntry->updatedBy->name }}
                        @endif
                    </small>
                    @endif
                    @if($journalEntry->posted_at)
                    <small class="text-muted d-block mb-2">
                        <strong>Posted:</strong> {{ $journalEntry->posted_at->format('M d, Y H:i') }}
                        @if($journalEntry->postedBy)
                            by {{ $journalEntry->postedBy->name }}
                        @endif
                    </small>
                    @endif
                    <small class="text-muted d-block">
                        <strong>Lines Count:</strong> {{ $journalEntryLines->count() }} lines
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

@if(session('success'))
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            {{ session('success') }}
        </div>
    </div>
</div>
@endif

@endsection

@push('styles')
<style>
@media print {
    .btn, .card-header .btn-group {
        display: none !important;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
@endpush
