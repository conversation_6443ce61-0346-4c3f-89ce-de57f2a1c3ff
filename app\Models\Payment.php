<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;

class Payment extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    // use BusinessTypeTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'amount',
        'debt',
        'credit',
        'comment',
        'description',
        'transaction_description',
        'created_by',
        'updated_by',
        'balance',
        'paymentable_id',
        'paymentable_type',
        'account_id',
        'reference_no',
        'currency_id',
        'payment_method',
        'payment_date',
    ];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];
    
    protected $searchableFields = ['*'];

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function paymentable()
    {
        return $this->morphTo();
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    /**
     * Get formatted description for transaction display
     */
    public function getTransactionDescriptionAttribute()
    {
        if ($this->description) {
            return $this->description;
        }

        if ($this->comment) {
            return $this->comment;
        }

        if ($this->paymentable) {
            $type = class_basename($this->paymentable_type);
            return "Payment for {$type} #{$this->paymentable_id}";
        }

        return "Payment";
    }

    /**
     * Get the payment type for display
     */
    public function getPaymentTypeAttribute()
    {
        if ($this->paymentable) {
            return class_basename($this->paymentable_type);
        }

        return 'General';
    }

    /**
     * Check if this is a bank transaction
     */
    public function isBankTransaction()
    {
        return $this->paymentable_type === BankAccount::class;
    }

    /**
     * Check if this is a credit (money in)
     */
    public function isCredit()
    {
        return $this->amount > 0;
    }

    /**
     * Check if this is a debit (money out)
     */
    public function isDebit()
    {
        return $this->amount < 0;
    }

    /**
     * Get the transaction type for display
     */
    public function getTransactionTypeAttribute()
    {
        if ($this->isBankTransaction()) {
            return $this->isCredit() ? 'Credit' : 'Debit';
        }
        return 'Payment';
    }

    /**
     * Get formatted amount for display
     */
    public function getFormattedAmountAttribute()
    {
        $symbol = $this->currency->symbol ?? '$';
        return $symbol . number_format(abs($this->amount), 2);
    }

    /**
     * Scope for bank transactions only
     */
    public function scopeBankTransactions($query)
    {
        return $query->where('paymentable_type', BankAccount::class);
    }

    /**
     * Scope for credit transactions (positive amounts)
     */
    public function scopeCredits($query)
    {
        return $query->where('amount', '>', 0);
    }

    /**
     * Scope for debit transactions (negative amounts)
     */
    public function scopeDebits($query)
    {
        return $query->where('amount', '<', 0);
    }

}
