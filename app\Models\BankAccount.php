<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;

class BankAccount extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BusinessTypeTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'name',
        'account_number',
        'bank_name',
        'branch_name',
        'swift_code',
        'iban',
        'account_type',
        'gl_account_id',
        'currency_id',
        'opening_balance',
        'opening_balance_date',
        'current_balance',
        'last_reconciliation_date',
        'is_active',
        'description',
        'created_by',
        'updated_by',
        'business_type_id',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'opening_balance' => 'decimal:2',
        'opening_balance_date' => 'date',
        'current_balance' => 'decimal:2',
        'last_reconciliation_date' => 'date',
        'is_active' => 'boolean',
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];

    public function glAccount()
    {
        return $this->belongsTo(Account::class, 'gl_account_id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function bankReconciliations()
    {
        return $this->hasMany(BankReconciliation::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get all bank transactions (payments) for this bank account
     */
    public function transactions()
    {
        return $this->morphMany(Payment::class, 'paymentable');
    }

    /**
     * Get the calculated balance based on opening balance and transactions
     */
    public function getCalculatedBalanceAttribute()
    {
        $transactionSum = $this->transactions()->sum('amount');
        return $this->opening_balance + $transactionSum;
    }

    /**
     * Update the current balance based on all transactions
     */
    public function updateCurrentBalance()
    {
        $this->current_balance = $this->calculated_balance;
        $this->save();
        return $this;
    }
}
