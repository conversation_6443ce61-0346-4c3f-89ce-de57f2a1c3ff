<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707 = $component; } ?>
<?php $component = App\View\Components\PageHeader::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\PageHeader::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('title', null, []); ?> 
            <i class="bi bi-journal-bookmark me-2"></i>Account Details
         <?php $__env->endSlot(); ?>
         <?php $__env->slot('controls', null, []); ?> 
            <div class="btn-group" role="group">
                <a href="<?php echo e(route('accounts.index')); ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Accounts
                </a>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $account)): ?>
                <a href="<?php echo e(route('accounts.edit', $account)); ?>" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-pencil me-1"></i>Edit Account
                </a>
                <?php endif; ?>
                <button type="button" class="btn btn-outline-info btn-sm" onclick="window.print()">
                    <i class="bi bi-printer me-1"></i>Print
                </button>
            </div>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707)): ?>
<?php $component = $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707; ?>
<?php unset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707); ?>
<?php endif; ?>
    <!-- End Page Header -->

    <div class="row">
        <!-- Account Overview -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i><?php echo e($account->code); ?> - <?php echo e($account->name); ?>

                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Account Code:</strong></td>
                                    <td><?php echo e($account->code); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Account Type:</strong></td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo e($account->accountType->name ?? 'N/A'); ?></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Category:</strong></td>
                                    <td><?php echo e($account->accountCategory->name ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Normal Balance:</strong></td>
                                    <td>
                                        <span class="badge <?php echo e($account->normal_balance == 'debit' ? 'bg-success' : 'bg-danger'); ?>">
                                            <?php echo e(ucfirst($account->normal_balance)); ?>

                                        </span>
                                    </td>
                                </tr>
                                <?php if($account->parentAccount): ?>
                                <tr>
                                    <td><strong>Parent Account:</strong></td>
                                    <td>
                                        <a href="<?php echo e(route('accounts.show', $account->parentAccount)); ?>">
                                            <?php echo e($account->parentAccount->code); ?> - <?php echo e($account->parentAccount->name); ?>

                                        </a>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <?php if($account->is_active): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                        
                                        <?php if($account->is_system): ?>
                                            <span class="badge bg-warning ms-1">System</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Manual Entries:</strong></td>
                                    <td>
                                        <?php if($account->allows_manual_entries): ?>
                                            <i class="bi bi-check-circle-fill text-success"></i> Allowed
                                        <?php else: ?>
                                            <i class="bi bi-x-circle-fill text-danger"></i> Not Allowed
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Opening Balance:</strong></td>
                                    <td>
                                        <?php if($account->opening_balance): ?>
                                            $<?php echo e(number_format($account->opening_balance, 2)); ?>

                                            <?php if($account->opening_balance_date): ?>
                                                <br><small class="text-muted">as of <?php echo e($account->opening_balance_date->format('M d, Y')); ?></small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            $0.00
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>
                                        <?php echo e($account->created_at->format('M d, Y')); ?>

                                        <?php if($account->createdBy): ?>
                                            <br><small class="text-muted">by <?php echo e($account->createdBy->name); ?></small>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if($account->description): ?>
                    <div class="mt-3">
                        <h6>Description:</h6>
                        <p class="text-muted"><?php echo e($account->description); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Direct Payments -->
            <?php if(isset($payments) && $payments->count() > 0): ?>
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul me-2"></i>Direct Payments
                    </h5>
                    <span class="badge bg-primary"><?php echo e($payments->count()); ?> payments</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Description</th>
                                    <th>Reference</th>
                                    <th class="text-end">Debit</th>
                                    <th class="text-end">Credit</th>
                                    <th class="text-end">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $payments->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($payment->payment_date ? $payment->payment_date->format('M d, Y') : $payment->created_at->format('M d, Y')); ?></td>
                                    <td><?php echo e($payment->description ?: $payment->comment ?: 'Payment'); ?></td>
                                    <td><?php echo e($payment->reference_no ?: '-'); ?></td>
                                    <td class="text-end">
                                        <?php if($payment->amount < 0): ?>
                                            <span class="text-success fw-bold">$<?php echo e(number_format(abs($payment->amount), 2)); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-end">
                                        <?php if($payment->amount > 0): ?>
                                            <span class="text-danger fw-bold">$<?php echo e(number_format($payment->amount, 2)); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-end">
                                        <span class="fw-bold <?php echo e($payment->amount >= 0 ? 'text-success' : 'text-danger'); ?>">
                                            $<?php echo e(number_format(abs($payment->amount), 2)); ?>

                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if($payments->count() > 10): ?>
                    <div class="text-center mt-3">
                        <a href="<?php echo e(route('payments.index')); ?>?account_id=<?php echo e($account->id); ?>" class="btn btn-outline-primary btn-sm">
                            View All Payments (<?php echo e($payments->count()); ?> total)
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Bank Account Payments -->
            <?php if(isset($bankAccountPayments) && $bankAccountPayments->count() > 0): ?>
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bank me-2"></i>Bank Account Transactions
                    </h5>
                    <span class="badge bg-info"><?php echo e($bankAccountPayments->count()); ?> transactions</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Bank Account</th>
                                    <th>Description</th>
                                    <th>Reference</th>
                                    <th class="text-end">Amount</th>
                                    <th class="text-center">Type</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $bankAccountPayments->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($payment->created_at->format('M d, Y')); ?></td>
                                    <td>
                                        <?php if($payment->paymentable): ?>
                                            <?php echo e($payment->paymentable->name); ?>

                                        <?php else: ?>
                                            N/A
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($payment->transaction_description); ?></td>
                                    <td><?php echo e($payment->reference_no ?: '-'); ?></td>
                                    <td class="text-end">
                                        <span class="fw-bold <?php echo e($payment->amount >= 0 ? 'text-success' : 'text-danger'); ?>">
                                            $<?php echo e(number_format(abs($payment->amount), 2)); ?>

                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-<?php echo e($payment->amount >= 0 ? 'success' : 'danger'); ?>">
                                            <?php echo e($payment->amount >= 0 ? 'Credit' : 'Debit'); ?>

                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <?php if($bankAccountPayments->count() > 10): ?>
                    <div class="text-center mt-3">
                        <a href="<?php echo e(route('cashbook.index')); ?>" class="btn btn-outline-primary btn-sm">
                            View All Transactions (<?php echo e($bankAccountPayments->count()); ?> total)
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Recent Journal Entries -->
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>Recent Journal Entries
                    </h5>
                    <?php if(Route::has('journal-entries.create')): ?>
                    <a href="<?php echo e(route('journal-entries.create')); ?>?account_id=<?php echo e($account->id); ?>" class="btn btn-primary btn-sm">
                        <i class="bi bi-plus-circle me-1"></i>Add Entry
                    </a>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if($journalEntryLines->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Entry #</th>
                                    <th>Description</th>
                                    <th class="text-end">Debit</th>
                                    <th class="text-end">Credit</th>
                                    <th class="text-center">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $journalEntryLines->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $line): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($line->journalEntry->entry_date->format('M d, Y')); ?></td>
                                    <td>
                                        <?php if(Route::has('journal-entries.show')): ?>
                                        <a href="<?php echo e(route('journal-entries.show', $line->journalEntry)); ?>">
                                            <?php echo e($line->journalEntry->entry_number); ?>

                                        </a>
                                        <?php else: ?>
                                            <?php echo e($line->journalEntry->entry_number); ?>

                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($line->description ?: $line->journalEntry->description); ?></td>
                                    <td class="text-end">
                                        <?php if($line->debit > 0): ?>
                                            <span class="text-success fw-bold">$<?php echo e(number_format($line->debit, 2)); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-end">
                                        <?php if($line->credit > 0): ?>
                                            <span class="text-danger fw-bold">$<?php echo e(number_format($line->credit, 2)); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-<?php echo e($line->journalEntry->status == 'posted' ? 'success' : 'warning'); ?>">
                                            <?php echo e(ucfirst($line->journalEntry->status)); ?>

                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <?php if($journalEntryLines->count() > 10): ?>
                    <div class="text-center mt-3">
                        <?php if(Route::has('journal-entries.index')): ?>
                        <a href="<?php echo e(route('journal-entries.index')); ?>?account_id=<?php echo e($account->id); ?>" class="btn btn-outline-primary btn-sm">
                            View All Entries (<?php echo e($journalEntryLines->count()); ?> total)
                        </a>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-journal-x display-4 text-muted d-block mb-2"></i>
                        <p class="text-muted mb-0">No journal entries found for this account.</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Account Statistics -->
        <div class="col-lg-4">
            <!-- Balance Card -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-calculator me-2"></i>Account Balance
                    </h6>
                </div>
                <div class="card-body text-center">
                    <h2 class="mb-1 <?php echo e($account->balance >= 0 ? 'text-success' : 'text-danger'); ?>">
                        $<?php echo e(number_format(abs($account->balance), 2)); ?>

                        <?php if($account->balance < 0): ?> <small>(CR)</small> <?php endif; ?>
                    </h2>
                    <p class="text-muted mb-0">Current Balance</p>
                    
                    <hr class="my-3">
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <h5 class="text-success mb-1">$<?php echo e(number_format($account->debit, 2)); ?></h5>
                            <small class="text-muted">Total Debits</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-danger mb-1">$<?php echo e(number_format($account->credit, 2)); ?></h5>
                            <small class="text-muted">Total Credits</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-lightning me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if(Route::has('journal-entries.create')): ?>
                        <a href="<?php echo e(route('journal-entries.create')); ?>?account_id=<?php echo e($account->id); ?>" class="btn btn-primary btn-sm">
                            <i class="bi bi-plus-circle me-1"></i>Add Journal Entry
                        </a>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $account)): ?>
                        <a href="<?php echo e(route('accounts.edit', $account)); ?>" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-pencil me-1"></i>Edit Account
                        </a>
                        <?php endif; ?>

                        <button type="button" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-graph-up me-1"></i>View Reports
                        </button>

                        <button type="button" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-download me-1"></i>Export Transactions
                        </button>

                        <?php if(!$account->is_system): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $account)): ?>
                        <form method="POST" action="<?php echo e(route('accounts.destroy', $account)); ?>" 
                              onsubmit="return confirm('Are you sure you want to delete this account?')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                <i class="bi bi-trash me-1"></i>Delete Account
                            </button>
                        </form>
                        <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Account Info -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Account Information
                    </h6>
                </div>
                <div class="card-body">
                    <small class="text-muted d-block mb-2">
                        <strong>Account ID:</strong> <?php echo e($account->id); ?>

                    </small>
                    <small class="text-muted d-block mb-2">
                        <strong>Full Name:</strong> <?php echo e($account->full_name); ?>

                    </small>
                    <?php if($account->updated_at != $account->created_at): ?>
                    <small class="text-muted d-block mb-2">
                        <strong>Last Updated:</strong> <?php echo e($account->updated_at->format('M d, Y H:i')); ?>

                        <?php if($account->updatedBy): ?>
                            by <?php echo e($account->updatedBy->name); ?>

                        <?php endif; ?>
                    </small>
                    <?php endif; ?>
                    <small class="text-muted d-block">
                        <strong>Entry Count:</strong> <?php echo e($journalEntryLines->count()); ?> journal entries
                        <?php if(isset($payments)): ?>
                            + <?php echo e($payments->count()); ?> direct payments
                        <?php endif; ?>
                        <?php if(isset($bankAccountPayments)): ?>
                            + <?php echo e($bankAccountPayments->count()); ?> bank payments
                        <?php endif; ?>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if(session('success')): ?>
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            <?php echo e(session('success')); ?>

        </div>
    </div>
</div>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
@media print {
    .btn, .card-header .btn-group {
        display: none !important;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Acounting\github\accounting\resources\views/app/accounts/show.blade.php ENDPATH**/ ?>