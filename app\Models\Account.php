<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;

class Account extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BusinessTypeTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'name',
        'code',
        'description',
        'account_type_id',
        'account_category_id',
        'parent_account_id',
        'is_active',
        'is_system',
        'allows_manual_entries',
        'normal_balance',
        'opening_balance',
        'opening_balance_date',
        'created_by',
        'updated_by',
        'business_type_id',
    ];

    protected $searchableFields = ['*'];

    protected $appends = ['debit', 'credit', 'balance', 'full_name'];

    protected $casts = [
        'is_active' => 'boolean',
        'is_system' => 'boolean',
        'allows_manual_entries' => 'boolean',
        'opening_balance' => 'decimal:2',
        'opening_balance_date' => 'date',
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];

    public function getFullNameAttribute()
    {
        return $this->code . ' - ' . $this->name;
    }

    public function accountType()
    {
        return $this->belongsTo(AccountType::class);
    }

    public function accountCategory()
    {
        return $this->belongsTo(AccountCategory::class);
    }

    public function parentAccount()
    {
        return $this->belongsTo(Account::class, 'parent_account_id');
    }

    public function childAccounts()
    {
        return $this->hasMany(Account::class, 'parent_account_id');
    }

    public function journalEntryLines()
    {
        return $this->hasMany(JournalEntryLine::class);
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    /**
     * Get direct payments for this account (polymorphic relationship)
     */
    public function payments()
    {
        return $this->morphMany(Payment::class, 'paymentable');
    }

    /**
     * Get payments related to this account through bank accounts
     * This is for accounts that are linked to bank accounts
     */
    public function bankAccountPayments()
    {
        return $this->hasManyThrough(
            Payment::class,
            BankAccount::class,
            'gl_account_id', // Foreign key on bank_accounts table
            'paymentable_id', // Foreign key on payments table
            'id', // Local key on accounts table
            'id' // Local key on bank_accounts table
        )->where('payments.paymentable_type', BankAccount::class);
    }

    /**
     * Get all bank accounts linked to this GL account
     */
    public function bankAccounts()
    {
        return $this->hasMany(BankAccount::class, 'gl_account_id');
    }

    public function getCreditAttribute() {
        // Credits from journal entries + positive direct payments + positive payments from linked bank accounts
        $journalCredits = $this->journalEntryLines()->sum('credit');

        // Direct payments to this account (positive amounts are credits)
        $directPaymentCredits = $this->payments()->where('amount', '>', 0)->sum('amount');

        // Bank account payments (positive amounts are credits)
        $bankCredits = $this->bankAccountPayments()->where('amount', '>', 0)->sum('amount');

        return $journalCredits + $directPaymentCredits + $bankCredits;
    }

    public function getDebitAttribute() {
        // Debits from journal entries + negative direct payments + negative payments from linked bank accounts
        $journalDebits = $this->journalEntryLines()->sum('debit');

        // Direct payments from this account (negative amounts are debits)
        $directPaymentDebits = abs($this->payments()->where('amount', '<', 0)->sum('amount'));

        // Bank account payments (negative amounts are debits)
        $bankDebits = abs($this->bankAccountPayments()->where('amount', '<', 0)->sum('amount'));

        return $journalDebits + $directPaymentDebits + $bankDebits;
    }

    public function getBalanceAttribute() {
        $balance = 0;
        
        // Include opening balance in the calculation
        $openingBalance = $this->opening_balance ?? 0;
        
        if ($this->normal_balance === 'debit') {
            $balance = $openingBalance + $this->debit - $this->credit;
        } else {
            $balance = $openingBalance + $this->credit - $this->debit;
        }

        return $balance;
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
