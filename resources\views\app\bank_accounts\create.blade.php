@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-plus-circle me-2"></i>Add Bank Account
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('bank-accounts.index') }}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-arrow-left me-1"></i>Back to Bank Accounts
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bank2 me-2"></i>New Bank Account
                    </h5>
                </div>

                <form method="POST" action="{{ route('bank-accounts.store') }}">
                    @csrf
                    <div class="card-body">
                        
                        <!-- Error Display -->
                        @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <h6 class="alert-heading">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Please fix the following errors:
                            </h6>
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        @endif

                        <!-- Basic Information -->
                        <h6 class="text-primary mb-3">
                            <i class="bi bi-info-circle me-2"></i>Basic Information
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Account Name <span class="text-danger">*</span></label>
                                <input type="text" name="name" class="form-control @error('name') is-invalid @enderror" 
                                       placeholder="e.g., Main Business Account" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Account Number <span class="text-danger">*</span></label>
                                <input type="text" name="account_number" class="form-control @error('account_number') is-invalid @enderror" 
                                       placeholder="e.g., **********" value="{{ old('account_number') }}" required>
                                @error('account_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Bank Name <span class="text-danger">*</span></label>
                                <input type="text" name="bank_name" class="form-control @error('bank_name') is-invalid @enderror" 
                                       placeholder="e.g., First National Bank" value="{{ old('bank_name') }}" required>
                                @error('bank_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Branch Name</label>
                                <input type="text" name="branch_name" class="form-control @error('branch_name') is-invalid @enderror" 
                                       placeholder="e.g., Downtown Branch" value="{{ old('branch_name') }}">
                                @error('branch_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Account Type <span class="text-danger">*</span></label>
                                <select name="account_type" class="form-select @error('account_type') is-invalid @enderror" required>
                                    <option value="">Select Account Type</option>
                                    <option value="checking" {{ old('account_type') == 'checking' ? 'selected' : '' }}>Checking</option>
                                    <option value="savings" {{ old('account_type') == 'savings' ? 'selected' : '' }}>Savings</option>
                                    <option value="money_market" {{ old('account_type') == 'money_market' ? 'selected' : '' }}>Money Market</option>
                                    <option value="certificate_of_deposit" {{ old('account_type') == 'certificate_of_deposit' ? 'selected' : '' }}>Certificate of Deposit</option>
                                    <option value="other" {{ old('account_type') == 'other' ? 'selected' : '' }}>Other</option>
                                </select>
                                @error('account_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Currency</label>
                                <select name="currency_id" class="form-select @error('currency_id') is-invalid @enderror">
                                    <option value="">Default Currency</option>
                                    @foreach($currencies as $id => $name)
                                        <option value="{{ $id }}" {{ old('currency_id') == $id ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('currency_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Banking Details -->
                        <h6 class="text-primary mb-3 mt-4">
                            <i class="bi bi-bank me-2"></i>Banking Details
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">SWIFT Code</label>
                                <input type="text" name="swift_code" class="form-control @error('swift_code') is-invalid @enderror" 
                                       placeholder="e.g., ABCDUS33XXX" value="{{ old('swift_code') }}">
                                @error('swift_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">IBAN</label>
                                <input type="text" name="iban" class="form-control @error('iban') is-invalid @enderror" 
                                       placeholder="e.g., GB82 WEST 1234 5698 7654 32" value="{{ old('iban') }}">
                                @error('iban')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Accounting Integration -->
                        <h6 class="text-primary mb-3 mt-4">
                            <i class="bi bi-calculator me-2"></i>Accounting Integration
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">GL Account <span class="text-danger">*</span></label>
                                <select name="gl_account_id" class="form-select @error('gl_account_id') is-invalid @enderror" required>
                                    <option value="">Select GL Account</option>
                                    @foreach($glAccounts as $id => $name)
                                        <option value="{{ $id }}" {{ old('gl_account_id') == $id ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('gl_account_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Link this bank account to a General Ledger account</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Status</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="is_active" value="1" 
                                           {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label">Active</label>
                                </div>
                                <small class="form-text text-muted">Only active accounts can be used for transactions</small>
                            </div>
                        </div>

                        <!-- Opening Balance -->
                        <h6 class="text-primary mb-3 mt-4">
                            <i class="bi bi-cash me-2"></i>Opening Balance
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Opening Balance <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" name="opening_balance" class="form-control @error('opening_balance') is-invalid @enderror" 
                                           step="0.01" placeholder="0.00" value="{{ old('opening_balance', '0.00') }}" required>
                                </div>
                                @error('opening_balance')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Opening Balance Date <span class="text-danger">*</span></label>
                                <input type="date" name="opening_balance_date" class="form-control @error('opening_balance_date') is-invalid @enderror" 
                                       value="{{ old('opening_balance_date', date('Y-m-d')) }}" required>
                                @error('opening_balance_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea name="description" class="form-control @error('description') is-invalid @enderror" 
                                      rows="3" placeholder="Additional notes about this bank account">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                    </div>

                    <div class="card-footer d-flex justify-content-between">
                        <a href="{{ route('bank-accounts.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>Create Bank Account
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
