@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-pencil me-2"></i>Edit Account
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group" role="group">
                <a href="{{ route('accounts.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Accounts
                </a>
                <a href="{{ route('accounts.show', $account) }}" class="btn btn-outline-info btn-sm">
                    <i class="bi bi-eye me-1"></i>View Account
                </a>
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-journal-bookmark me-2"></i>Edit: {{ $account->code }} - {{ $account->name }}
                    </h5>
                </div>

                <form method="POST" action="{{ route('accounts.update', $account) }}">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        
                        <!-- Error Display -->
                        @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <h6 class="alert-heading">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Please fix the following errors:
                            </h6>
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        @endif

                        <!-- Current Balance Display -->
                        <div class="alert alert-info">
                            <div class="row">
                                <div class="col-md-4">
                                    <strong>Current Balance:</strong> 
                                    <span class="fw-bold {{ $account->balance >= 0 ? 'text-success' : 'text-danger' }}">
                                        ${{ number_format(abs($account->balance), 2) }}
                                        @if($account->balance < 0) (CR) @endif
                                    </span>
                                </div>
                                <div class="col-md-4">
                                    <strong>Total Debits:</strong> ${{ number_format($account->debit, 2) }}
                                </div>
                                <div class="col-md-4">
                                    <strong>Total Credits:</strong> ${{ number_format($account->credit, 2) }}
                                </div>
                            </div>
                        </div>

                        <!-- Basic Information -->
                        <h6 class="text-primary mb-3">
                            <i class="bi bi-info-circle me-2"></i>Basic Information
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Account Code <span class="text-danger">*</span></label>
                                <input type="text" name="code" class="form-control @error('code') is-invalid @enderror" 
                                       placeholder="e.g., 1000" value="{{ old('code', $account->code) }}" required>
                                @error('code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Unique account code for identification</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Account Name <span class="text-danger">*</span></label>
                                <input type="text" name="name" class="form-control @error('name') is-invalid @enderror" 
                                       placeholder="e.g., Cash in Bank" value="{{ old('name', $account->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Classification -->
                        <h6 class="text-primary mb-3 mt-4">
                            <i class="bi bi-tags me-2"></i>Classification
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Account Type <span class="text-danger">*</span></label>
                                <select name="account_type_id" class="form-select @error('account_type_id') is-invalid @enderror" required>
                                    <option value="">Select Account Type</option>
                                    @if(isset($accountTypes))
                                        @foreach($accountTypes as $id => $name)
                                            <option value="{{ $id }}" {{ old('account_type_id', $account->account_type_id) == $id ? 'selected' : '' }}>
                                                {{ $name }}
                                            </option>
                                        @endforeach
                                    @endif
                                </select>
                                @error('account_type_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Account Category <span class="text-danger">*</span></label>
                                <select name="account_category_id" class="form-select @error('account_category_id') is-invalid @enderror" required>
                                    <option value="">Select Category</option>
                                    @if(isset($accountCategories))
                                        @foreach($accountCategories as $id => $name)
                                            <option value="{{ $id }}" {{ old('account_category_id', $account->account_category_id) == $id ? 'selected' : '' }}>
                                                {{ $name }}
                                            </option>
                                        @endforeach
                                    @endif
                                </select>
                                @error('account_category_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Parent Account</label>
                                <select name="parent_account_id" class="form-select @error('parent_account_id') is-invalid @enderror">
                                    <option value="">No Parent (Top Level)</option>
                                    @if(isset($parentAccounts))
                                        @foreach($parentAccounts as $id => $name)
                                            <option value="{{ $id }}" {{ old('parent_account_id', $account->parent_account_id) == $id ? 'selected' : '' }}>
                                                {{ $name }}
                                            </option>
                                        @endforeach
                                    @endif
                                </select>
                                @error('parent_account_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Optional: Create as sub-account</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Normal Balance <span class="text-danger">*</span></label>
                                <select name="normal_balance" class="form-select @error('normal_balance') is-invalid @enderror" required>
                                    <option value="">Select Normal Balance</option>
                                    <option value="debit" {{ old('normal_balance', $account->normal_balance) == 'debit' ? 'selected' : '' }}>Debit</option>
                                    <option value="credit" {{ old('normal_balance', $account->normal_balance) == 'credit' ? 'selected' : '' }}>Credit</option>
                                </select>
                                @error('normal_balance')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Assets/Expenses = Debit, Liabilities/Equity/Revenue = Credit</small>
                            </div>
                        </div>

                        <!-- Account Settings -->
                        <h6 class="text-primary mb-3 mt-4">
                            <i class="bi bi-gear me-2"></i>Account Settings
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Currency</label>
                                <select name="currency_id" class="form-select @error('currency_id') is-invalid @enderror">
                                    <option value="">Default Currency</option>
                                    @if(isset($currencies))
                                        @foreach($currencies as $id => $name)
                                            <option value="{{ $id }}" {{ old('currency_id', $account->currency_id) == $id ? 'selected' : '' }}>
                                                {{ $name }}
                                            </option>
                                        @endforeach
                                    @endif
                                </select>
                                @error('currency_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Tax Rate (%)</label>
                                <input type="number" name="tax_rate" class="form-control @error('tax_rate') is-invalid @enderror" 
                                       step="0.01" min="0" max="100" placeholder="0.00" value="{{ old('tax_rate', $account->tax_rate ?? 0) }}">
                                @error('tax_rate')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Default tax rate for this account</small>
                            </div>
                        </div>

                        <!-- Account Behavior -->
                        <h6 class="text-primary mb-3 mt-4">
                            <i class="bi bi-toggles me-2"></i>Account Behavior
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="is_active" value="1" 
                                           {{ old('is_active', $account->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label">Active Account</label>
                                </div>
                                <small class="form-text text-muted">Only active accounts can be used in transactions</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="allows_manual_entries" value="1" 
                                           {{ old('allows_manual_entries', $account->allows_manual_entries) ? 'checked' : '' }}>
                                    <label class="form-check-label">Allow Manual Entries</label>
                                </div>
                                <small class="form-text text-muted">Allow manual journal entries to this account</small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="is_system" value="1" 
                                           {{ old('is_system', $account->is_system) ? 'checked' : '' }}
                                           @if($account->is_system) disabled @endif>
                                    <label class="form-check-label">System Account</label>
                                </div>
                                <small class="form-text text-muted">System accounts cannot be deleted</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="requires_reconciliation" value="1" 
                                           {{ old('requires_reconciliation', $account->requires_reconciliation) ? 'checked' : '' }}>
                                    <label class="form-check-label">Requires Reconciliation</label>
                                </div>
                                <small class="form-text text-muted">Account requires periodic reconciliation</small>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea name="description" class="form-control @error('description') is-invalid @enderror" 
                                      rows="3" placeholder="Account description and purpose">{{ old('description', $account->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Notes -->
                        <div class="mb-3">
                            <label class="form-label">Notes</label>
                            <textarea name="notes" class="form-control @error('notes') is-invalid @enderror" 
                                      rows="2" placeholder="Additional notes or instructions">{{ old('notes', $account->notes) }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Account Info -->
                        <div class="alert alert-light">
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong>Created:</strong> {{ $account->created_at->format('M d, Y H:i') }}
                                        @if($account->createdBy)
                                            by {{ $account->createdBy->name }}
                                        @endif
                                    </small>
                                </div>
                                @if($account->updated_at != $account->created_at)
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong>Last Updated:</strong> {{ $account->updated_at->format('M d, Y H:i') }}
                                        @if($account->updatedBy)
                                            by {{ $account->updatedBy->name }}
                                        @endif
                                    </small>
                                </div>
                                @endif
                            </div>
                        </div>

                    </div>

                    <div class="card-footer d-flex justify-content-between">
                        <a href="{{ route('accounts.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>Update Account
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
