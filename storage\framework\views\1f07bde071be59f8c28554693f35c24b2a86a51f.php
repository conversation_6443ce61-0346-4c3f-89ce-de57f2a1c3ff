<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707 = $component; } ?>
<?php $component = App\View\Components\PageHeader::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\PageHeader::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('title', null, []); ?> 
            <i class="bi bi-bank me-2"></i>Cashbook Management
         <?php $__env->endSlot(); ?>
         <?php $__env->slot('controls', null, []); ?> 
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Payment::class)): ?>
            <div class="btn-group" role="group">
                <a href="<?php echo e(route('cashbook.create')); ?>" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>Add Bank Transaction
                </a>
                <?php if(Route::has('bank-accounts.index')): ?>
                <a href="<?php echo e(route('bank-accounts.index')); ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-bank2 me-1"></i>Manage Banks
                </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707)): ?>
<?php $component = $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707; ?>
<?php unset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707); ?>
<?php endif; ?>
    <!-- End Page Header -->

    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-funnel me-2"></i>Filters
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('cashbook.index')); ?>">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Bank Account</label>
                        <select name="bank_account_id" class="form-select">
                            <option value="">All Bank Accounts</option>
                            <?php $__currentLoopData = $bankAccounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($account->id); ?>" 
                                    <?php echo e($bankAccountId == $account->id ? 'selected' : ''); ?>>
                                    <?php echo e($account->name); ?> (<?php echo e($account->account_number); ?>)
                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Date From</label>
                        <input type="date" name="date_from" class="form-control" value="<?php echo e($dateFrom); ?>">
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Date To</label>
                        <input type="date" name="date_to" class="form-control" value="<?php echo e($dateTo); ?>">
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">Search</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="Search description, reference..." value="<?php echo e($search); ?>">
                    </div>
                    
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>Filter
                        </button>
                        <a href="<?php echo e(route('cashbook.index')); ?>" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bank Account Summary -->
    <?php if($selectedBankAccount): ?>
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title mb-1"><?php echo e($selectedBankAccount->name); ?></h5>
                            <p class="card-text mb-0"><?php echo e($selectedBankAccount->account_number); ?></p>
                            <small class="opacity-75"><?php echo e($selectedBankAccount->bank_name); ?></small>
                        </div>
                        <div class="text-end">
                            <h3 class="mb-0">
                                <?php echo e($selectedBankAccount->currency->symbol ?? '$'); ?><?php echo e(number_format($runningBalance, 2)); ?>

                            </h3>
                            <small class="opacity-75">Current Balance</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-success mb-1">
                                <?php echo e($selectedBankAccount->currency->symbol ?? '$'); ?><?php echo e(number_format($transactions->where('amount', '>', 0)->sum('amount'), 2)); ?>

                            </h4>
                            <small class="text-muted">Total Credits</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-danger mb-1">
                                <?php echo e($selectedBankAccount->currency->symbol ?? '$'); ?><?php echo e(number_format(abs($transactions->where('amount', '<', 0)->sum('amount')), 2)); ?>

                            </h4>
                            <small class="text-muted">Total Debits</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Transactions Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>Bank Transactions
            </h5>
            <span class="badge bg-secondary"><?php echo e($transactions->total()); ?> transactions</span>
        </div>
        
        <div class="table-responsive">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>Date</th>
                        <th>Bank Account</th>
                        <th>Description</th>
                        <th>Reference</th>
                        <th class="text-end">Debit</th>
                        <th class="text-end">Credit</th>
                        <th class="text-end">Balance</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td>
                            <span class="d-block"><?php echo e($transaction->created_at->format('M d, Y')); ?></span>
                            <small class="text-muted"><?php echo e($transaction->created_at->format('H:i')); ?></small>
                        </td>
                        <td>
                            <div>
                                <strong><?php echo e($transaction->paymentable->name ?? 'N/A'); ?></strong>
                                <br>
                                <small class="text-muted"><?php echo e($transaction->paymentable->account_number ?? ''); ?></small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong><?php echo e($transaction->description); ?></strong>
                                <?php if($transaction->comment): ?>
                                    <br><small class="text-muted"><?php echo e($transaction->comment); ?></small>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <?php echo e($transaction->reference_no ?? '-'); ?>

                        </td>
                        <td class="text-end">
                            <?php if($transaction->amount < 0): ?>
                                <span class="text-danger fw-bold">
                                    <?php echo e($transaction->currency->symbol ?? '$'); ?><?php echo e(number_format(abs($transaction->amount), 2)); ?>

                                </span>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-end">
                            <?php if($transaction->amount > 0): ?>
                                <span class="text-success fw-bold">
                                    <?php echo e($transaction->currency->symbol ?? '$'); ?><?php echo e(number_format($transaction->amount, 2)); ?>

                                </span>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-end">
                            <span class="fw-bold">
                                <?php echo e($transaction->currency->symbol ?? '$'); ?><?php echo e(number_format($transaction->paymentable->current_balance ?? 0, 2)); ?>

                            </span>
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $transaction)): ?>
                                <a href="<?php echo e(route('cashbook.edit', $transaction)); ?>" 
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <?php endif; ?>
                                
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $transaction)): ?>
                                <form method="POST" action="<?php echo e(route('cashbook.destroy', $transaction)); ?>" 
                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this transaction?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-inbox display-4 d-block mb-2"></i>
                                <p class="mb-0">No bank transactions found.</p>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Payment::class)): ?>
                                <a href="<?php echo e(route('cashbook.create')); ?>" class="btn btn-primary btn-sm mt-2">
                                    <i class="bi bi-plus-circle me-1"></i>Add First Transaction
                                </a>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if($transactions->hasPages()): ?>
        <div class="card-footer">
            <?php echo e($transactions->appends(request()->query())->links()); ?>

        </div>
        <?php endif; ?>
    </div>
</div>

<?php if(session('success')): ?>
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            <?php echo e(session('success')); ?>

        </div>
    </div>
</div>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Acounting\github\accounting\resources\views/app/cashbook/index.blade.php ENDPATH**/ ?>