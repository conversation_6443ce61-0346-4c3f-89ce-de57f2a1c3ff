@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-calendar3 me-2"></i>Fiscal Periods
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group" role="group">
                @can('create', App\Models\FiscalPeriod::class)
                <a href="{{ route('fiscal-periods.create') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>Add Period
                </a>
                @endcan
                <a href="{{ route('fiscal-years.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-calendar-range me-1"></i>Fiscal Years
                </a>
                <button type="button" class="btn btn-outline-info btn-sm" onclick="window.print()">
                    <i class="bi bi-printer me-1"></i>Print
                </button>
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-funnel me-2"></i>Filters & Search
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('fiscal-periods.index') }}">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Search</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="Search periods..." value="{{ $search ?? '' }}">
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">Fiscal Year</label>
                        <select name="fiscal_year_id" class="form-select">
                            <option value="">All Years</option>
                            @foreach($fiscalYears ?? [] as $id => $name)
                                <option value="{{ $id }}" {{ request('fiscal_year_id') == $id ? 'selected' : '' }}>
                                    {{ $name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="open" {{ request('status') == 'open' ? 'selected' : '' }}>Open</option>
                            <option value="closed" {{ request('status') == 'closed' ? 'selected' : '' }}>Closed</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Active</label>
                        <select name="is_active" class="form-select">
                            <option value="">All</option>
                            <option value="1" {{ request('is_active') == '1' ? 'selected' : '' }}>Active</option>
                            <option value="0" {{ request('is_active') == '0' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>Filter
                        </button>
                        <a href="{{ route('fiscal-periods.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Fiscal Periods Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>Fiscal Periods
            </h5>
            <span class="badge bg-secondary">{{ $fiscalPeriods->total() }} periods</span>
        </div>
        
        <div class="table-responsive">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>Period</th>
                        <th>Fiscal Year</th>
                        <th>Duration</th>
                        <th>Days</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Active</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($fiscalPeriods as $period)
                    <tr>
                        <td>
                            <div>
                                <strong>
                                    <a href="{{ route('fiscal-periods.show', $period) }}" class="text-decoration-none">
                                        {{ $period->name }}
                                    </a>
                                </strong>
                                @if($period->description)
                                    <br><small class="text-muted">{{ $period->description }}</small>
                                @endif
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>{{ $period->fiscalYear->name ?? 'N/A' }}</strong>
                                <br>
                                <small class="text-muted">
                                    {{ $period->fiscalYear->start_date->format('Y') ?? '' }} - 
                                    {{ $period->fiscalYear->end_date->format('Y') ?? '' }}
                                </small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>{{ $period->start_date->format('M d') }}</strong>
                                <br>
                                <small class="text-muted">to {{ $period->end_date->format('M d, Y') }}</small>
                            </div>
                        </td>
                        <td>
                            @php
                                $duration = $period->start_date->diffInDays($period->end_date) + 1;
                            @endphp
                            <span class="fw-bold">{{ $duration }}</span>
                        </td>
                        <td class="text-center">
                            @if($period->is_closed)
                                <span class="badge bg-danger">Closed</span>
                            @else
                                <span class="badge bg-success">Open</span>
                            @endif
                        </td>
                        <td class="text-center">
                            @if($period->is_active)
                                <span class="badge bg-success">
                                    <i class="bi bi-check-circle me-1"></i>Active
                                </span>
                            @else
                                <span class="badge bg-secondary">
                                    <i class="bi bi-x-circle me-1"></i>Inactive
                                </span>
                            @endif
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                @can('view', $period)
                                <a href="{{ route('fiscal-periods.show', $period) }}" 
                                   class="btn btn-sm btn-outline-info" title="View Details">
                                    <i class="bi bi-eye"></i>
                                </a>
                                @endcan
                                
                                @can('update', $period)
                                @if(!$period->is_closed)
                                <a href="{{ route('fiscal-periods.edit', $period) }}" 
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                @endif
                                @endcan
                                
                                @can('delete', $period)
                                @if(!$period->is_closed)
                                <form method="POST" action="{{ route('fiscal-periods.destroy', $period) }}" 
                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this fiscal period?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                @endif
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-calendar3 display-4 d-block mb-2"></i>
                                <p class="mb-0">No fiscal periods found.</p>
                                @can('create', App\Models\FiscalPeriod::class)
                                <a href="{{ route('fiscal-periods.create') }}" class="btn btn-primary btn-sm mt-2">
                                    <i class="bi bi-plus-circle me-1"></i>Create First Period
                                </a>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($fiscalPeriods->hasPages())
        <div class="card-footer">
            {{ $fiscalPeriods->appends(request()->query())->links() }}
        </div>
        @endif
    </div>

    <!-- Summary Cards -->
    @if($fiscalPeriods->count() > 0)
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $fiscalPeriods->where('is_closed', false)->count() }}</h3>
                    <small>Open Periods</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $fiscalPeriods->where('is_closed', true)->count() }}</h3>
                    <small>Closed Periods</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $fiscalPeriods->where('is_active', true)->count() }}</h3>
                    <small>Active Periods</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $fiscalPeriods->count() }}</h3>
                    <small>Total Periods</small>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

@if(session('success'))
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            {{ session('success') }}
        </div>
    </div>
</div>
@endif

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
@endpush
