<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            // Add debt and credit columns for double-entry accounting
            $table->decimal('debt', 15, 2)->default(0)->after('amount');
            $table->decimal('credit', 15, 2)->default(0)->after('debt');
            
            // Add account_id for direct account transactions
            $table->foreignId('account_id')->nullable()->after('paymentable_type')->constrained('accounts')->onDelete('cascade');
            
            // Add transaction description for accounting purposes
            $table->string('transaction_description')->nullable()->after('description');
            
            // Add indexes for better performance
            $table->index(['account_id', 'created_at']);
            $table->index(['paymentable_type', 'paymentable_id']);
            $table->index('reference_no');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->dropForeign(['account_id']);
            $table->dropIndex(['account_id', 'created_at']);
            $table->dropIndex(['paymentable_type', 'paymentable_id']);
            $table->dropIndex(['reference_no']);
            
            $table->dropColumn([
                'debt',
                'credit', 
                'account_id',
                'transaction_description'
            ]);
        });
    }
};
