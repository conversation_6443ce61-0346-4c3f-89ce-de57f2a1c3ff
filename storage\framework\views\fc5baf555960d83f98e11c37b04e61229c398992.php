<aside class="js-navbar-vertical-aside navbar navbar-vertical-aside navbar-vertical navbar-vertical-fixed navbar-expand-xl navbar-bordered bg-white">
  <div class="navbar-vertical-container">
    <div class="navbar-vertical-footer-offset">
      <!-- Logo -->
      <a class="navbar-brand" href="/" aria-label="A & L">
        <?php if(auth()->user()->getGlobalInstance('logo')): ?>
        <img class="navbar-brand-logo" style="max-height: 100px" src="<?php echo e(auth()->user()->getGlobalInstance('logo')->path); ?>" alt="Logo" data-hs-theme-appearance="default">
        <img class="navbar-brand-logo" style="max-height: 100px" src="<?php echo e(auth()->user()->getGlobalInstance('logo')->path); ?>" alt="Logo" data-hs-theme-appearance="dark">
        <?php endif; ?>
        <span class="m-1"><?php echo e(\App\Models\Tenant::current()->name ?? 'Your Business'); ?></span>
      </a>
      <!-- End Logo -->

      <!-- Navbar Vertical Toggle -->
      <button type="button" class="js-navbar-vertical-aside-toggle-invoker navbar-aside-toggler">
        <i class="bi-arrow-bar-left navbar-toggler-short-align" data-bs-template='<div class="tooltip d-none d-md-block" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>' data-bs-toggle="tooltip" data-bs-placement="right" title="Collapse"></i>
        <i class="bi-arrow-bar-right navbar-toggler-full-align" data-bs-template='<div class="tooltip d-none d-md-block" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>' data-bs-toggle="tooltip" data-bs-placement="right" title="Expand"></i>
      </button>
      <!-- End Navbar Vertical Toggle -->

      <!-- Content -->
      <div class="navbar-vertical-content">
        <div id="navbarVerticalMenu" class="nav nav-pills nav-vertical card-navbar-nav">
          <!-- Dashboard -->
          <div id="navbarVerticalDashboardMenu" class="nav-item">
            <a class="nav-link <?php if(request()->is('/')): ?> active <?php endif; ?>" href="/" data-placement="left">
              <i class="bi-house-door nav-icon"></i>
              <span class="nav-link-title">Dashboard</span>
            </a>
          </div>

          <!-- Pending Approvals -->
          <?php
              $managedWarehouses = 0;
              $totalPendingApprovals = 0;

              if (auth()->user()->isSuperAdmin()) {
                  $managedWarehouses = \App\Models\Warehouse::count();
              } else {
                  $managedWarehouses = \App\Models\Warehouse::where(function($query) {
                      $query->where('manager_id', auth()->id())
                            ->orWhere('created_by', auth()->id())
                            ->orWhere('branch_id', auth()->user()->branch_id);
                  })->count();
              }

              if ($managedWarehouses > 0) {
                  $managedWarehouseIds = auth()->user()->isSuperAdmin() ?
                      \App\Models\Warehouse::pluck('id') :
                      \App\Models\Warehouse::where(function($query) {
                          $query->where('manager_id', auth()->id())
                                ->orWhere('created_by', auth()->id())
                                ->orWhere('branch_id', auth()->user()->branch_id);
                      })->pluck('id');

                  $totalPendingApprovals = \App\Models\Stock::where('quantity', '>', 0)
                                                          ->whereNotNull('stock_id')
                                                          ->whereNull('approved_by')
                                                          ->whereIn('warehouse_id', $managedWarehouseIds)
                                                          ->count();
              }
          ?>

          <?php if($managedWarehouses > 0 || auth()->user()->hasRole(['manager', 'supervisor', 'accountant'])): ?>
          <div id="navbarVerticalApprovalsMenu" class="nav-item">
            <a class="nav-link <?php if(request()->is('pending-approvals*')): ?> active <?php endif; ?> position-relative" href="<?php echo e(route('approvals.index')); ?>" data-placement="left">
              <i class="bi-clock-history nav-icon"></i>
              <span class="nav-link-title">Pending Approvals</span>
              <!-- <?php if($totalPendingApprovals > 0): ?>
                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.6rem;">
                  <?php echo e($totalPendingApprovals); ?>

                  <span class="visually-hidden">pending approvals</span>
                </span>
              <?php endif; ?> -->
            </a>
          </div>
          <?php endif; ?>

          <!-- Sales & Customer Management -->
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\SalesOrder::class)): ?>
          <span class="dropdown-header mt-4">Sales & Customers</span>
          <small class="bi-graph-up nav-subtitle-replacer"></small>

          <!-- Quick Sale Action -->
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Invoice::class)): ?>
          <div class="nav-item">
            <a class="nav-link <?php if(request()->is('invoices/create')): ?> active <?php endif; ?>" href="/invoices/create" data-placement="left">
              <i class="bi-plus-circle-fill nav-icon"></i>
              <span class="nav-link-title">New Sale</span>
            </a>
          </div>
          <?php endif; ?>

          <!-- Sales Management -->
          <div class="nav-item">
            <a class="nav-link dropdown-toggle" href="#navbarVerticalMenuSalesMenu" role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalMenuSalesMenu" aria-expanded="false" aria-controls="navbarVerticalMenuSalesMenu">
              <i class="bi-cart3 nav-icon"></i>
              <span class="nav-link-title">Sales Management</span>
            </a>
            <div id="navbarVerticalMenuSalesMenu" class="nav-collapse collapse <?php if(request()->is('invoices*', 'quotations*', 'sales-orders*', 'sales-returns*', 'pricing-rules*', 'customers*')): ?> show <?php endif; ?>" data-bs-parent="#navbarVerticalMenu">
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Invoice::class)): ?>
              <a class="nav-link <?php if(request()->is('invoices') && !request()->is('invoices/create')): ?> active <?php endif; ?>" href="/invoices" data-placement="left">
                <i class="bi-receipt nav-icon"></i>
                <span class="nav-link-title">All Invoices</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Quotation::class)): ?>
              <a class="nav-link <?php if(request()->is('quotations')): ?> active <?php endif; ?>" href="/quotations" data-placement="left">
                <i class="bi-file-earmark-text nav-icon"></i>
                <span class="nav-link-title">Quotations</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Shipment::class)): ?>
              <a class="nav-link <?php if(request()->is('shipments')): ?> active <?php endif; ?>" href="/shipments" data-placement="left">
                <i class="bi-truck nav-icon"></i>
                <span class="nav-link-title">Shipments</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Customer::class)): ?>
              <a class="nav-link <?php if(request()->is('customers')): ?> active <?php endif; ?>" href="/customers" data-placement="left">
                <i class="bi-truck nav-icon"></i>
                <span class="nav-link-title">Customers</span>
              </a>
              <?php endif; ?>
              <!-- <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\SalesOrder::class)): ?>
              <a class="nav-link <?php if(request()->is('sales-orders')): ?> active <?php endif; ?>" href="/sales-orders" data-placement="left">
                <i class="bi-clipboard-check nav-icon"></i>
                <span class="nav-link-title">Sales Orders</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\SalesReturn::class)): ?>
              <a class="nav-link <?php if(request()->is('sales-returns')): ?> active <?php endif; ?>" href="/sales-returns" data-placement="left">
                <i class="bi-arrow-counterclockwise nav-icon"></i>
                <span class="nav-link-title">Returns</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\PricingRule::class)): ?>
              <a class="nav-link <?php if(request()->is('pricing-rules')): ?> active <?php endif; ?>" href="/pricing-rules" data-placement="left">
                <i class="bi-tags nav-icon"></i>
                <span class="nav-link-title">Pricing Rules</span>
              </a>
              <?php endif; ?> -->
            </div>
          <?php endif; ?>

          <!-- Inventory Module -->
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Product::class)): ?>
          <span class="dropdown-header mt-4">Inventory</span>
          <small class="bi-box nav-subtitle-replacer"></small>
          <div class="nav-item">
            <a class="nav-link dropdown-toggle" href="#navbarVerticalMenuInventoryMenu" role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalMenuInventoryMenu" aria-expanded="false" aria-controls="navbarVerticalMenuInventoryMenu">
              <i class="bi-box nav-icon"></i>
              <span class="nav-link-title">Inventory Management</span>
            </a>
            <div id="navbarVerticalMenuInventoryMenu" class="nav-collapse collapse <?php if(request()->is('products*', 'stocks*', 'warehouses*', 'locations*', 'stock-adjustments*', 'stock-transfers*')): ?> show <?php endif; ?>" data-bs-parent="#navbarVerticalMenu">
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Product::class)): ?>
              <a class="nav-link <?php if(request()->is('products')): ?> active <?php endif; ?>" href="/products/create" data-placement="left">
                <i class="bi-box nav-icon"></i>
                <span class="nav-link-title">Product Entry</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Product::class)): ?>
              <a class="nav-link <?php if(request()->is('products')): ?> active <?php endif; ?>" href="/products" data-placement="left">
                <i class="bi-box nav-icon"></i>
                <span class="nav-link-title">Inventory Summary</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Stock::class)): ?>
              <a class="nav-link <?php if(request()->is('stocks') && !request()->is('stocks/transfers*')): ?> active <?php endif; ?>" href="/stocks" data-placement="left">
                <i class="bi-boxes nav-icon"></i>
                <span class="nav-link-title">Inventory</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Stock::class)): ?>
              <a class="nav-link <?php if(request()->is('stocks/transfers*')): ?> active <?php endif; ?>" href="<?php echo e(route('stocks.transfers')); ?>" data-placement="left">
                <i class="bi-arrow-left-right nav-icon"></i>
                <span class="nav-link-title">Transfers</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Warehouse::class)): ?>
              <a class="nav-link <?php if(request()->is('warehouses')): ?> active <?php endif; ?>" href="/warehouses" data-placement="left">
                <i class="bi-building nav-icon"></i>
                <span class="nav-link-title">Warehouses</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Location::class)): ?>
              <a class="nav-link <?php if(request()->is('locations')): ?> active <?php endif; ?>" href="<?php echo e(route('locations.index')); ?>" data-placement="left">
                <i class="bi-geo-alt nav-icon"></i>
                <span class="nav-link-title">Locations</span>
              </a>
              <?php endif; ?>

              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Category::class)): ?>
              <a class="nav-link <?php if(request()->is('categories')): ?> active <?php endif; ?>" href="/categories" data-placement="left">
                <i class="bi-tags nav-icon"></i>
                <span class="nav-link-title">Categories</span>
              </a>
              <?php endif; ?>
            </div>
          </div>
          <?php endif; ?>

          <!-- Purchases -->
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Order::class)): ?>
          <span class="dropdown-header mt-4">Purchases</span>
          <small class="bi-basket nav-subtitle-replacer"></small>
          <div class="nav-item">
            <a class="nav-link dropdown-toggle" href="#navbarVerticalMenuPurchasesMenu" role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalMenuPurchasesMenu" aria-expanded="false" aria-controls="navbarVerticalMenuPurchasesMenu">
              <i class="bi-basket nav-icon"></i>
              <span class="nav-link-title">Purchases</span>
            </a>
            <div id="navbarVerticalMenuPurchasesMenu" class="nav-collapse collapse <?php if(request()->is('orders*', 'canceled-orders*')): ?> show <?php endif; ?>" data-bs-parent="#navbarVerticalMenu">
              <a class="nav-link <?php if(request()->is('orders')): ?> active <?php endif; ?>" href="/orders" data-placement="left">
                <i class="bi-cart-plus nav-icon"></i>
                <span class="nav-link-title">Purchase Orders</span>
              </a>
              <a class="nav-link <?php if(request()->is('suppliers')): ?> active <?php endif; ?>" href="/suppliers" data-placement="left">
                <i class="bi-truck nav-icon"></i>
                <span class="nav-link-title">Suppliers</span>
              </a>
            </div>
          </div>
          <?php endif; ?>

          <!-- Accounts Receivable/Payable -->
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Debt::class)): ?>
          <span class="dropdown-header mt-4">Receivables/Payables</span>
          <small class="bi-cash nav-subtitle-replacer"></small>
          <div class="nav-item">
            <a class="nav-link dropdown-toggle" href="#navbarVerticalMenuDebtorsMenu" role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalMenuDebtorsMenu" aria-expanded="false" aria-controls="navbarVerticalMenuDebtorsMenu">
              <i class="bi-cash nav-icon"></i>
              <span class="nav-link-title">Receivables/Payables</span>
            </a>
            <div id="navbarVerticalMenuDebtorsMenu" class="nav-collapse collapse <?php if(request()->is('receivables*', 'supplier-debtors*', 'transactions*', 'cashbook*')): ?> show <?php endif; ?>" data-bs-parent="#navbarVerticalMenu">
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Customer::class)): ?>
              <a class="nav-link <?php if(request()->is('receivables')): ?> active <?php endif; ?>" href="/receivables" data-placement="left">
                <i class="bi-arrow-down-circle nav-icon"></i>
                <span class="nav-link-title">Accounts Receivable</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Supplier::class)): ?>
              <a class="nav-link <?php if(request()->is('payables*')): ?> active <?php endif; ?>" href="/payables" data-placement="left">
                <i class="bi-arrow-up-circle nav-icon"></i>
                <span class="nav-link-title">Accounts Payable</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Payment::class)): ?>
              <a class="nav-link <?php if(request()->is('cashbook*')): ?> active <?php endif; ?>" href="<?php echo e(route('cashbook.index')); ?>" data-placement="left">
                <i class="bi-bank nav-icon"></i>
                <span class="nav-link-title">Cashbook</span>
              </a>
              <a class="nav-link <?php if(request()->is('payments*')): ?> active <?php endif; ?>" href="/payments" data-placement="left">
                <i class="bi-arrow-left-right nav-icon"></i>
                <span class="nav-link-title">Payments</span>
              </a>
              <?php endif; ?>
            </div>
          </div>
          <?php endif; ?>

          <!-- Accounting Module -->
          <?php if(auth()->user()->can('view-any', App\Models\Account::class) || auth()->user()->can('view-any', App\Models\JournalEntry::class) || auth()->user()->can('view-any', App\Models\Asset::class) || auth()->user()->can('view-any', App\Models\BankAccount::class) || auth()->user()->can('view-any', App\Models\TaxType::class) || auth()->user()->can('view-any', App\Models\Budget::class) || auth()->user()->can('view-any', App\Models\BillOfMaterial::class)): ?>
          <span class="dropdown-header mt-4"><?php echo e(__('crud.nav.accounting')); ?></span>
          <small class="bi-calculator nav-subtitle-replacer"></small>

          <!-- Chart of Accounts -->
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Account::class)): ?>
          <div class="nav-item">
            <a class="nav-link dropdown-toggle" href="#navbarVerticalMenuChartOfAccountsMenu" role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalMenuChartOfAccountsMenu" aria-expanded="false" aria-controls="navbarVerticalMenuChartOfAccountsMenu">
              <i class="bi-journal-text nav-icon"></i>
              <span class="nav-link-title"><?php echo e(__('crud.chart_of_accounts.name')); ?></span>
            </a>
            <div id="navbarVerticalMenuChartOfAccountsMenu" class="nav-collapse collapse <?php if(request()->is('account-types*', 'account-categories*', 'accounts*')): ?> show <?php endif; ?>" data-bs-parent="#navbarVerticalMenu">
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\AccountType::class)): ?>
              <a class="nav-link <?php if(request()->is('account-types*')): ?> active <?php endif; ?>" href="<?php echo e(route('account-types.index')); ?>" data-placement="left">
                <i class="bi-tag nav-icon"></i>
                <span class="nav-link-title"><?php echo e(__('crud.account_types.name')); ?></span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\AccountCategory::class)): ?>
              <a class="nav-link <?php if(request()->is('account-categories*')): ?> active <?php endif; ?>" href="<?php echo e(route('account-categories.index')); ?>" data-placement="left">
                <i class="bi-folder nav-icon"></i>
                <span class="nav-link-title"><?php echo e(__('crud.account_categories.name')); ?></span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Account::class)): ?>
              <a class="nav-link <?php if(request()->is('accounts*')): ?> active <?php endif; ?>" href="<?php echo e(route('accounts.index')); ?>" data-placement="left">
                <i class="bi-journal-bookmark nav-icon"></i>
                <span class="nav-link-title"><?php echo e(__('crud.accounts.name')); ?></span>
              </a>
              <?php endif; ?>
            </div>
          </div>
          <?php endif; ?>

          <!-- General Ledger -->
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\JournalEntry::class)): ?>
          <div class="nav-item">
            <a class="nav-link dropdown-toggle" href="#navbarVerticalMenuGeneralLedgerMenu" role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalMenuGeneralLedgerMenu" aria-expanded="false" aria-controls="navbarVerticalMenuGeneralLedgerMenu">
              <i class="bi-book nav-icon"></i>
              <span class="nav-link-title">General Ledger</span>
            </a>
            <div id="navbarVerticalMenuGeneralLedgerMenu" class="nav-collapse collapse <?php if(request()->is('fiscal-years*', 'fiscal-periods*', 'journal-entries*')): ?> show <?php endif; ?>" data-bs-parent="#navbarVerticalMenu">
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\FiscalYear::class)): ?>
              <a class="nav-link <?php if(request()->is('fiscal-years')): ?> active <?php endif; ?>" href="/fiscal-years" data-placement="left">
                <i class="bi-calendar-range nav-icon"></i>
                <span class="nav-link-title">Fiscal Years</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\FiscalPeriod::class)): ?>
              <a class="nav-link <?php if(request()->is('fiscal-periods')): ?> active <?php endif; ?>" href="/fiscal-periods" data-placement="left">
                <i class="bi-calendar-month nav-icon"></i>
                <span class="nav-link-title">Fiscal Periods</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\JournalEntry::class)): ?>
              <a class="nav-link <?php if(request()->is('journal-entries')): ?> active <?php endif; ?>" href="/journal-entries" data-placement="left">
                <i class="bi-journal-plus nav-icon"></i>
                <span class="nav-link-title">Journal Entries</span>
              </a>
              <?php endif; ?>
            </div>
          </div>
          <?php endif; ?>

          <!-- Fixed Assets -->
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Asset::class)): ?>
          <div class="nav-item">
            <a class="nav-link dropdown-toggle" href="#navbarVerticalMenuFixedAssetsMenu" role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalMenuFixedAssetsMenu" aria-expanded="false" aria-controls="navbarVerticalMenuFixedAssetsMenu">
              <i class="bi-building nav-icon"></i>
              <span class="nav-link-title">Fixed Assets</span>
            </a>
            <div id="navbarVerticalMenuFixedAssetsMenu" class="nav-collapse collapse <?php if(request()->is('asset-categories*', 'assets*', 'asset-depreciations*')): ?> show <?php endif; ?>" data-bs-parent="#navbarVerticalMenu">
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\AssetCategory::class)): ?>
              <a class="nav-link <?php if(request()->is('asset-categories')): ?> active <?php endif; ?>" href="/asset-categories" data-placement="left">
                <i class="bi-folder nav-icon"></i>
                <span class="nav-link-title">Asset Categories</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Asset::class)): ?>
              <a class="nav-link <?php if(request()->is('assets') && !request()->is('asset-*')): ?> active <?php endif; ?>" href="/assets" data-placement="left">
                <i class="bi-pc-display nav-icon"></i>
                <span class="nav-link-title">Assets</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\AssetDepreciation::class)): ?>
              <a class="nav-link <?php if(request()->is('asset-depreciations')): ?> active <?php endif; ?>" href="/asset-depreciations" data-placement="left">
                <i class="bi-graph-down nav-icon"></i>
                <span class="nav-link-title">Asset Depreciations</span>
              </a>
              <?php endif; ?>
            </div>
          </div>
          <?php endif; ?>

          <!-- Banking -->
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\BankAccount::class)): ?>
          <div class="nav-item">
            <a class="nav-link dropdown-toggle" href="#navbarVerticalMenuBankingMenu" role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalMenuBankingMenu" aria-expanded="false" aria-controls="navbarVerticalMenuBankingMenu">
              <i class="bi-bank nav-icon"></i>
              <span class="nav-link-title">Banking</span>
            </a>
            <div id="navbarVerticalMenuBankingMenu" class="nav-collapse collapse <?php if(request()->is('bank-accounts*', 'bank-reconciliations*')): ?> show <?php endif; ?>" data-bs-parent="#navbarVerticalMenu">
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\BankAccount::class)): ?>
              <a class="nav-link <?php if(request()->is('bank-accounts')): ?> active <?php endif; ?>" href="/bank-accounts" data-placement="left">
                <i class="bi-credit-card nav-icon"></i>
                <span class="nav-link-title">Bank Accounts</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\BankReconciliation::class)): ?>
              <a class="nav-link <?php if(request()->is('bank-reconciliations')): ?> active <?php endif; ?>" href="/bank-reconciliations" data-placement="left">
                <i class="bi-check2-square nav-icon"></i>
                <span class="nav-link-title">Bank Reconciliations</span>
              </a>
              <?php endif; ?>
            </div>
          </div>
          <?php endif; ?>

          <!-- Tax Management -->
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\TaxType::class)): ?>
          <div class="nav-item">
            <a class="nav-link dropdown-toggle" href="#navbarVerticalMenuTaxMenu" role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalMenuTaxMenu" aria-expanded="false" aria-controls="navbarVerticalMenuTaxMenu">
              <i class="bi-receipt nav-icon"></i>
              <span class="nav-link-title">Tax Management</span>
            </a>
            <div id="navbarVerticalMenuTaxMenu" class="nav-collapse collapse <?php if(request()->is('tax-types*', 'tax-transactions*')): ?> show <?php endif; ?>" data-bs-parent="#navbarVerticalMenu">
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\TaxType::class)): ?>
              <a class="nav-link <?php if(request()->is('tax-types')): ?> active <?php endif; ?>" href="/tax-types" data-placement="left">
                <i class="bi-percent nav-icon"></i>
                <span class="nav-link-title">Tax Types</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\TaxTransaction::class)): ?>
              <a class="nav-link <?php if(request()->is('tax-transactions') && !request()->is('tax-transactions/report*')): ?> active <?php endif; ?>" href="/tax-transactions" data-placement="left">
                <i class="bi-card-list nav-icon"></i>
                <span class="nav-link-title">Tax Transactions</span>
              </a>
              <?php endif; ?>
            </div>
          </div>
          <?php endif; ?>

          <!-- Budgeting -->
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Budget::class)): ?>
          <div class="nav-item">
            <a class="nav-link dropdown-toggle" href="#navbarVerticalMenuBudgetMenu" role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalMenuBudgetMenu" aria-expanded="false" aria-controls="navbarVerticalMenuBudgetMenu">
              <i class="bi-graph-up nav-icon"></i>
              <span class="nav-link-title">Budgeting</span>
            </a>
            <div id="navbarVerticalMenuBudgetMenu" class="nav-collapse collapse <?php if(request()->is('budgets*')): ?> show <?php endif; ?>" data-bs-parent="#navbarVerticalMenu">
              <a class="nav-link <?php if(request()->is('budgets') && !request()->is('budgets/report*')): ?> active <?php endif; ?>" href="/budgets" data-placement="left">
                <i class="bi-clipboard-data nav-icon"></i>
                <span class="nav-link-title">Budgets</span>
              </a>
            </div>
          </div>
          <?php endif; ?>

          <!-- Human Resources -->
          <?php if(auth()->user()->can('view-any', App\Models\Employee::class) || auth()->user()->can('view-any', App\Models\MonthlyPayroll::class)): ?>
          <span class="dropdown-header mt-4">Human Resources</span>
          <small class="bi-people nav-subtitle-replacer"></small>

          <!-- Employee Management -->
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Employee::class)): ?>
          <div class="nav-item">
            <a class="nav-link <?php if(request()->is('employees*') && !request()->is('employee-deduction-contributions*')): ?> active <?php endif; ?>" href="<?php echo e(route('employees.index')); ?>" data-placement="left">
              <i class="bi-person-badge nav-icon"></i>
              <span class="nav-link-title">Employees</span>
            </a>
          </div>
          <?php endif; ?>

          <!-- Payroll -->
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\MonthlyPayroll::class)): ?>
          <div class="nav-item">
            <a class="nav-link dropdown-toggle" href="#navbarVerticalMenuPayrollMenu" role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalMenuPayrollMenu" aria-expanded="false" aria-controls="navbarVerticalMenuPayrollMenu">
              <i class="bi-cash-coin nav-icon"></i>
              <span class="nav-link-title">Payroll</span>
            </a>
            <div id="navbarVerticalMenuPayrollMenu" class="nav-collapse collapse <?php if(request()->is('payroll*', 'deduction-contributions*', 'employee-deduction-contributions*', 'tax-brackets*', 'time-tracking*', 'work-hours-configurations*', 'employee-loans*')): ?> show <?php endif; ?>" data-bs-parent="#navbarVerticalMenu">
              <a class="nav-link <?php if(request()->is('payroll') && !request()->is('payroll/generate*')): ?> active <?php endif; ?>" href="<?php echo e(route('payroll.index')); ?>" data-placement="left">
                <i class="bi-table nav-icon"></i>
                <span class="nav-link-title">Payroll List</span>
              </a>
              <a class="nav-link <?php if(request()->is('payroll/generate*')): ?> active <?php endif; ?>" href="<?php echo e(route('payroll.generate')); ?>" data-placement="left">
                <i class="bi-calculator nav-icon"></i>
                <span class="nav-link-title">Generate Payroll</span>
              </a>
              <a class="nav-link <?php if(request()->is('payroll/journal-entries/create*')): ?> active <?php endif; ?>" href="<?php echo e(route('payroll.create-journal-entries')); ?>" data-placement="left">
                <i class="bi-journal-plus nav-icon"></i>
                <span class="nav-link-title">Create Journal Entries</span>
              </a>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('viewAny', App\Models\EmployeeLoan::class)): ?>
              <a class="nav-link <?php if(request()->is('employee-loans*')): ?> active <?php endif; ?>" href="<?php echo e(route('employee-loans.index')); ?>" data-placement="left">
                <i class="bi-bank nav-icon"></i>
                <span class="nav-link-title">Employee Loans</span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\TaxBracket::class)): ?>
              <a class="nav-link <?php if(request()->is('tax-brackets*')): ?> active <?php endif; ?>" href="<?php echo e(route('tax-brackets.index')); ?>" data-placement="left">
                <i class="bi-percent nav-icon"></i>
                <span class="nav-link-title">Tax Brackets</span>
              </a>
              <?php endif; ?>
              <a class="nav-link <?php if(request()->is('deduction-contributions*') && request()->query('type') == 'deduction'): ?> active <?php endif; ?>" href="<?php echo e(route('deduction-contributions.index', ['type' => 'deduction'])); ?>" data-placement="left">
                <i class="bi-dash-circle-fill nav-icon"></i>
                <span class="nav-link-title">Deductions</span>
              </a>
              <a class="nav-link <?php if(request()->is('deduction-contributions*') && request()->query('type') == 'contribution'): ?> active <?php endif; ?>" href="<?php echo e(route('deduction-contributions.index', ['type' => 'contribution'])); ?>" data-placement="left">
                <i class="bi-plus-circle-fill nav-icon"></i>
                <span class="nav-link-title">Contributions</span>
              </a>
              <a class="nav-link <?php if(request()->is('time-tracking*')): ?> active <?php endif; ?>" href="<?php echo e(route('time-tracking.index')); ?>" data-placement="left">
                <i class="bi-clock nav-icon"></i>
                <span class="nav-link-title">Time Tracking</span>
              </a>
              <a class="nav-link <?php if(request()->is('work-hours-configurations*')): ?> active <?php endif; ?>" href="<?php echo e(route('work-hours-configurations.index')); ?>" data-placement="left">
                <i class="bi-clock-history nav-icon"></i>
                <span class="nav-link-title">Work Hours Config</span>
              </a>
            </div>
          </div>
          <?php endif; ?>
          <?php endif; ?>

          <!-- Manufacturing -->
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\BillOfMaterial::class)): ?>
          <span class="dropdown-header mt-4"><?php echo e(__('crud.nav.production')); ?></span>
          <small class="bi-gear nav-subtitle-replacer"></small>
          <div class="nav-item">
            <a class="nav-link dropdown-toggle" href="#navbarVerticalMenuManufacturingMenu" role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalMenuManufacturingMenu" aria-expanded="false" aria-controls="navbarVerticalMenuManufacturingMenu">
              <i class="bi-gear nav-icon"></i>
              <span class="nav-link-title"><?php echo e(__('crud.nav.production')); ?></span>
            </a>
            <div id="navbarVerticalMenuManufacturingMenu" class="nav-collapse collapse <?php if(request()->is('boms*', 'production-orders*', 'material-issues*', 'wip-tracking*')): ?> show <?php endif; ?>" data-bs-parent="#navbarVerticalMenu">
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\BillOfMaterial::class)): ?>
              <a class="nav-link <?php if(request()->is('boms*')): ?> active <?php endif; ?>" href="<?php echo e(route('boms.index')); ?>" data-placement="left">
                <i class="bi-diagram-3 nav-icon"></i>
                <span class="nav-link-title"><?php echo e(__('crud.bill_of_materials.name')); ?></span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\WorkOrder::class)): ?>
              <a class="nav-link <?php if(request()->is('production-orders*')): ?> active <?php endif; ?>" href="<?php echo e(route('production-orders.index')); ?>" data-placement="left">
                <i class="bi-clipboard-check nav-icon"></i>
                <span class="nav-link-title"><?php echo e(__('crud.production_orders.name')); ?></span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\MaterialIssue::class)): ?>
              <a class="nav-link <?php if(request()->is('material-issues*')): ?> active <?php endif; ?>" href="/material-issues" data-placement="left">
                <i class="bi-box-arrow-right nav-icon"></i>
                <span class="nav-link-title"><?php echo e(__('crud.material_issues.name')); ?></span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\WIPTracking::class)): ?>
              <a class="nav-link <?php if(request()->is('wip-tracking*')): ?> active <?php endif; ?>" href="/wip-tracking" data-placement="left">
                <i class="bi-gear-wide-connected nav-icon"></i>
                <span class="nav-link-title"><?php echo e(__('crud.wip_tracking.name')); ?></span>
              </a>
              <?php endif; ?>
            </div>
          </div>
          <?php endif; ?>
          <?php endif; ?>

          <!-- Reports -->
          <?php if(auth()->user()->can('view-any', App\Models\SalesOrder::class) || auth()->user()->can('view-any', App\Models\Product::class) || auth()->user()->can('view-any', App\Models\Debt::class) || auth()->user()->can('view-any', App\Models\Budget::class) || auth()->user()->can('view-any', App\Models\TaxTransaction::class)): ?>
          <span class="dropdown-header mt-4"><?php echo e(__('crud.nav.reports')); ?></span>
          <small class="bi-file-earmark-bar-graph nav-subtitle-replacer"></small>
          <div class="nav-item">
            <a class="nav-link dropdown-toggle" href="#navbarVerticalMenuReportsMenu" role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalMenuReportsMenu" aria-expanded="false" aria-controls="navbarVerticalMenuReportsMenu">
              <i class="bi-file-earmark-bar-graph nav-icon"></i>
              <span class="nav-link-title"><?php echo e(__('crud.nav.reports')); ?></span>
            </a>
            <div id="navbarVerticalMenuReportsMenu" class="nav-collapse collapse <?php if(request()->is('*-report', 'debts', 'financial-reports*', 'tax-transactions/report*', 'budgets/report*', 'payroll-reports*')): ?> show <?php endif; ?>" data-bs-parent="#navbarVerticalMenu">
              <!-- Sales Reports -->
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\SalesOrder::class)): ?>
              <a class="nav-link <?php if(request()->is('sales-report')): ?> active <?php endif; ?>" href="/sales-report" data-placement="left">
                <i class="bi-graph-up nav-icon"></i>
                <span class="nav-link-title">Sales Report</span>
              </a>
              <a class="nav-link <?php if(request()->is('sales-order-status-report')): ?> active <?php endif; ?>" href="/sales-order-status-report" data-placement="left">
                <i class="bi-list-check nav-icon"></i>
                <span class="nav-link-title">Sales Order Status</span>
              </a>
              <a class="nav-link <?php if(request()->is('sales-returns-report')): ?> active <?php endif; ?>" href="/sales-returns-report" data-placement="left">
                <i class="bi-arrow-return-left nav-icon"></i>
                <span class="nav-link-title">Sales vs Returns</span>
              </a>
              <?php endif; ?>
              <!-- Inventory Reports -->
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Product::class)): ?>
              <a class="nav-link <?php if(request()->is('stock-report')): ?> active <?php endif; ?>" href="/stock-report" data-placement="left">
                <i class="bi-box nav-icon"></i>
                <span class="nav-link-title">Inventory Valuation</span>
              </a>
              <a class="nav-link <?php if(request()->is('stock-movement-report')): ?> active <?php endif; ?>" href="/stock-movement-report" data-placement="left">
                <i class="bi-arrow-left-right nav-icon"></i>
                <span class="nav-link-title">Stock Movement</span>
              </a>
              <?php endif; ?>
              <!-- Receivables/Payables Reports -->
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Debt::class)): ?>
              <a class="nav-link <?php if(request()->is('debts')): ?> active <?php endif; ?>" href="/debts" data-placement="left">
                <i class="bi-cash-stack nav-icon"></i>
                <span class="nav-link-title">Receivables/Payables Report</span>
              </a>
              <?php endif; ?>
              <!-- Financial Reports -->
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\JournalEntry::class)): ?>
              <a class="nav-link <?php if(request()->is('financial-reports/trial-balance')): ?> active <?php endif; ?>" href="/financial-reports/trial-balance" data-placement="left">
                <i class="bi-list-check nav-icon"></i>
                <span class="nav-link-title">Trial Balance</span>
              </a>
              <a class="nav-link <?php if(request()->is('financial-reports/balance-sheet')): ?> active <?php endif; ?>" href="/financial-reports/balance-sheet" data-placement="left">
                <i class="bi-file-earmark-spreadsheet nav-icon"></i>
                <span class="nav-link-title">Balance Sheet</span>
              </a>
              <a class="nav-link <?php if(request()->is('financial-reports/income-statement')): ?> active <?php endif; ?>" href="/financial-reports/income-statement" data-placement="left">
                <i class="bi-graph-up nav-icon"></i>
                <span class="nav-link-title">Income Statement</span>
              </a>
              <?php endif; ?>
              <!-- Payroll Reports -->
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\MonthlyPayroll::class)): ?>
              <a class="nav-link <?php if(request()->is('payroll-reports/journal')): ?> active <?php endif; ?>" href="<?php echo e(route('payroll-reports.journal-form')); ?>" data-placement="left">
                <i class="bi-journal-text nav-icon"></i>
                <span class="nav-link-title">Payroll Journal</span>
              </a>
              <a class="nav-link <?php if(request()->is('payroll-reports/liabilities')): ?> active <?php endif; ?>" href="<?php echo e(route('payroll-reports.liabilities-form')); ?>" data-placement="left">
                <i class="bi-cash-stack nav-icon"></i>
                <span class="nav-link-title">Payroll Liabilities</span>
              </a>
              <a class="nav-link <?php if(request()->is('payroll-reports/cost-summary')): ?> active <?php endif; ?>" href="<?php echo e(route('payroll-reports.cost-summary-form')); ?>" data-placement="left">
                <i class="bi-bar-chart-line nav-icon"></i>
                <span class="nav-link-title">Employee Cost Summary</span>
              </a>
              <?php endif; ?>
              <!-- Tax Reports -->
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\TaxTransaction::class)): ?>
              <a class="nav-link <?php if(request()->is('tax-transactions/report')): ?> active <?php endif; ?>" href="/tax-transactions/report" data-placement="left">
                <i class="bi-receipt nav-icon"></i>
                <span class="nav-link-title">Tax Reports</span>
              </a>
              <?php endif; ?>
              <!-- Budget Reports -->
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Budget::class)): ?>
              <a class="nav-link <?php if(request()->is('budgets/report')): ?> active <?php endif; ?>" href="/budgets/report" data-placement="left">
                <i class="bi-clipboard-data nav-icon"></i>
                <span class="nav-link-title">Budget vs Actual</span>
              </a>
              <?php endif; ?>
            </div>
          </div>
          <?php endif; ?>

          <!-- Settings -->
          <?php if(auth()->user()->can('view-any', App\Models\User::class) || auth()->user()->can('view-any', App\Models\Role::class) || auth()->user()->can('view-any', App\Models\Branch::class) || auth()->user()->can('view-any', App\Models\Currency::class) || auth()->user()->can('view-any', App\Models\BusinessType::class)): ?>
          <span class="dropdown-header mt-4"><?php echo e(__('crud.nav.settings')); ?></span>
          <small class="bi-gear nav-subtitle-replacer"></small>
          <div class="nav-item">
            <a class="nav-link dropdown-toggle" href="#navbarVerticalMenuSettingsMenu" role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalMenuSettingsMenu" aria-expanded="false" aria-controls="navbarVerticalMenuSettingsMenu">
              <i class="bi-gear nav-icon"></i>
              <span class="nav-link-title"><?php echo e(__('crud.nav.settings')); ?></span>
            </a>
            <div id="navbarVerticalMenuSettingsMenu" class="nav-collapse collapse <?php if(request()->is('users*', 'roles*', 'business-types*', 'permissions*', 'branches*', 'currencies*')): ?> show <?php endif; ?>" data-bs-parent="#navbarVerticalMenu">
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\User::class)): ?>
              <a class="nav-link <?php if(request()->is('users*')): ?> active <?php endif; ?>" href="<?php echo e(route('users.index')); ?>" data-placement="left">
                <i class="bi-person nav-icon"></i>
                <span class="nav-link-title"><?php echo e(__('crud.users.name')); ?></span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Role::class)): ?>
              <a class="nav-link <?php if(request()->is('roles*')): ?> active <?php endif; ?>" href="<?php echo e(route('roles.index')); ?>" data-placement="left">
                <i class="bi-shield-check nav-icon"></i>
                <span class="nav-link-title"><?php echo e(__('crud.roles.name')); ?></span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Permission::class)): ?>
              <a class="nav-link <?php if(request()->is('permissions*')): ?> active <?php endif; ?>" href="<?php echo e(route('permissions.index')); ?>" data-placement="left">
                <i class="bi-key nav-icon"></i>
                <span class="nav-link-title"><?php echo e(__('crud.permissions.name')); ?></span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Branch::class)): ?>
              <a class="nav-link <?php if(request()->is('branches*')): ?> active <?php endif; ?>" href="<?php echo e(route('branches.index')); ?>" data-placement="left">
                <i class="bi-diagram-3 nav-icon"></i>
                <span class="nav-link-title"><?php echo e(__('crud.branches.name')); ?></span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Currency::class)): ?>
              <a class="nav-link <?php if(request()->is('currencies*')): ?> active <?php endif; ?>" href="<?php echo e(route('currencies.index')); ?>" data-placement="left">
                <i class="bi-cash-coin nav-icon"></i>
                <span class="nav-link-title"><?php echo e(__('crud.currencies.name')); ?></span>
              </a>
              <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\BusinessType::class)): ?>
              <a class="nav-link <?php if(request()->is('business-types*')): ?> active <?php endif; ?>" href="<?php echo e(route('business-types.index')); ?>" data-placement="left">
                <i class="bi-briefcase nav-icon"></i>
                <span class="nav-link-title"><?php echo e(__('crud.business_types.name')); ?></span>
              </a>
              <?php endif; ?>
            </div>
          </div>
          <?php endif; ?>

          <!-- Footer -->
          <div class="navbar-vertical-footer px-0">
            <ul class="navbar-vertical-footer-list">
              <!-- Tenant Switcher -->
              <?php if(auth()->check() && auth()->user()->getAvailableTenants()->count() > 0): ?>
              <li class="navbar-vertical-footer-list-item">
                <div class="dropdown dropup">
                  <button type="button" class="btn btn-ghost-secondary btn-icon rounded-circle" id="tenantSwitcherDropdown" data-bs-toggle="dropdown" aria-expanded="false" data-bs-dropdown-animation title="Current Tenant & Switch">
                    <i class="bi-building"></i>
                  </button>
                  <div class="dropdown-menu navbar-dropdown-menu-borderless" aria-labelledby="tenantSwitcherDropdown" style="min-width: 280px;">
                    <span class="dropdown-header">
                      <i class="bi-building me-1"></i>Your Tenants
                      <span class="badge bg-info ms-2"><?php echo e(auth()->user()->getAvailableTenants()->count()); ?></span>
                    </span>
                    <div class="dropdown-divider"></div>

                    <?php $currentTenant = \App\Models\Tenant::current(); ?>

                    <?php $__currentLoopData = auth()->user()->getAvailableTenants(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <a class="dropdown-item <?php echo e($currentTenant->id === $tenant->id ? 'active' : ''); ?>"
                         href="<?php echo e(route('tenant.switch', $tenant->id)); ?>"
                         onclick="switchTenant('<?php echo e($tenant->id); ?>'); return false;">
                        <div class="d-flex align-items-center">
                          <div class="flex-shrink-0">
                            <i class="bi-building dropdown-item-icon <?php echo e($tenant['is_primary'] ? 'text-primary' : ($currentTenant === $tenant->id ? 'text-success' : '')); ?>"></i>
                          </div>
                          <div class="flex-grow-1 ms-2">
                            <div class="d-flex justify-content-between align-items-center">
                              <?php if($currentTenant->id == $tenant->id): ?>                              
                              <span class="fw-bold text-primary"  title="<?php echo e(ucfirst(str_replace('_', ' ', $tenant->name??''))); ?>"><?php echo e(ucfirst(str_replace('_', ' ', $tenant->name??''))); ?></span>
                              <?php else: ?>
                              <span class="text-truncate" title="<?php echo e(ucfirst(str_replace('_', ' ', $tenant->name??''))); ?>"><?php echo e(ucfirst(str_replace('_', ' ', $tenant->name??''))); ?></span>
                              <?php endif; ?>
                              <div class="d-flex gap-1">
                                <?php if($tenant['is_primary']): ?>
                                  <span class="badge bg-primary">Primary</span>
                                <?php endif; ?>
                                <?php if($currentTenant === $tenant->id): ?>
                                  <span class="badge bg-success">Active</span>
                                <?php endif; ?>
                              </div>
                            </div>
                            <small class="text-muted"><?php echo e(ucfirst($tenant['role'])); ?> • Joined <?php echo e(\Carbon\Carbon::parse($tenant['joined_at'])->format('M Y')); ?></small>
                          </div>
                          <?php if($currentTenant === $tenant->id): ?>
                            <div class="flex-shrink-0 ms-2">
                              <i class="bi-check-circle text-success"></i>
                            </div>
                          <?php endif; ?>
                        </div>
                      </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <?php if(auth()->user()->getAvailableTenants()->count() === 1): ?>
                      <div class="dropdown-divider"></div>
                      <div class="dropdown-item-text text-center">
                        <small class="text-muted">
                          <i class="bi-info-circle me-1"></i>
                          You have access to 1 tenant
                        </small>
                      </div>
                    <?php endif; ?>
                  </div>
                </div>
              </li>
              <?php endif; ?>

              <li class="navbar-vertical-footer-list-item">
                <div class="dropdown dropup">
                  <button type="button" class="btn btn-ghost-secondary btn-icon rounded-circle" id="selectThemeDropdown" data-bs-toggle="dropdown" aria-expanded="false" data-bs-dropdown-animation></button>
                  <div class="dropdown-menu navbar-dropdown-menu navbar-dropdown-menu-borderless" aria-labelledby="selectThemeDropdown">
                    <a class="dropdown-item" href="#" data-icon="bi-moon-stars" data-value="auto">
                      <i class="bi-moon-stars me-2"></i>
                      <span class="text-truncate" title="Auto (system default)">Auto (system default)</span>
                    </a>
                    <a class="dropdown-item" href="#" data-icon="bi-brightness-high" data-value="default">
                      <i class="bi-brightness-high me-2"></i>
                      <span class="text-truncate" title="Default (light mode)">Default (light mode)</span>
                    </a>
                    <a class="dropdown-item active" href="#" data-icon="bi-moon" data-value="dark">
                      <i class="bi-moon me-2"></i>
                      <span class="text-truncate" title="Dark">Dark</span>
                    </a>
                  </div>
                </div>
              </li>
              <li class="navbar-vertical-footer-list-item">
                <div class="dropdown dropup">
                  <button type="button" class="btn btn-ghost-secondary btn-icon rounded-circle" id="otherLinksDropdown" data-bs-toggle="dropdown" aria-expanded="false" data-bs-dropdown-animation>
                    <i class="bi-info-circle"></i>
                  </button>
                  <div class="dropdown-menu navbar-dropdown-menu-borderless" aria-labelledby="otherLinksDropdown">
                    <span class="dropdown-header">Help</span>
                    <a class="dropdown-item" href="#">
                      <i class="bi-journals dropdown-item-icon"></i>
                      <span class="text-truncate" title="Resources & tutorials">Resources & tutorials</span>
                    </a>
                    <a class="dropdown-item" href="#">
                      <i class="bi-command dropdown-item-icon"></i>
                      <span class="text-truncate" title="Keyboard shortcuts">Keyboard shortcuts</span>
                    </a>
                  </div>
                </div>
              </li>
              <li class="navbar-vertical-footer-list-item">
                <div class="dropdown dropup">
                  <button type="button" class="btn btn-ghost-secondary btn-icon rounded-circle" id="selectLanguageDropdown" data-bs-toggle="dropdown" aria-expanded="false" data-bs-dropdown-animation>
                    <img class="avatar avatar-xss avatar-circle" src="<?php echo e(asset('assets/vendor/flag-icon-css/flags/1x1/us.svg')); ?>" alt="United States Flag">
                  </button>
                  <div class="dropdown-menu navbar-dropdown-menu-borderless" aria-labelledby="selectLanguageDropdown">
                    <span class="dropdown-header">Select language</span>
                    <a class="dropdown-item" href="#">
                      <img class="avatar avatar-xss avatar-circle me-2" src="<?php echo e(asset('assets/vendor/flag-icon-css/flags/1x1/us.svg')); ?>" alt="Flag">
                      <span class="text-truncate" title="English">English (US)</span>
                    </a>
                    <a class="dropdown-item" href="#">
                      <img class="avatar avatar-xss avatar-circle me-2" src="<?php echo e(asset('assets/vendor/flag-icon-css/flags/1x1/gb.svg')); ?>" alt="Flag">
                      <span class="text-truncate" title="English">English (UK)</span>
                    </a>
                  </div>
                </div>
              </li>
            </ul>
          </div>
          <!-- End Footer -->
        </div>
      </div>
      <!-- End Content -->
    </div>
  </div>
</aside>

<?php $__env->startPush('scripts'); ?>
<script>
// Tenant switching functionality
function switchTenant(tenantId) {
    // Show loading state
    const button = document.getElementById('tenantSwitcherDropdown');
    const originalIcon = button.innerHTML;
    button.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
    button.disabled = true;

    // Make the switch request
    fetch(`/tenant/switch/${tenantId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success notification
            showNotification.success(data.message || 'Tenant switched successfully!');

            // Redirect to dashboard or reload page
            setTimeout(() => {
                if (data.redirect_url) {
                    window.location.href = data.redirect_url;
                } else {
                    window.location.reload();
                }
            }, 1000);
        } else {
            // Show error notification
            showNotification.error(data.message || 'Failed to switch tenant');

            // Restore button state
            button.innerHTML = originalIcon;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error switching tenant:', error);

        // Show error notification
        showNotification.error('Failed to switch tenant. Please try again.');

        // Restore button state
        button.innerHTML = originalIcon;
        button.disabled = false;
    });
}

// Initialize tenant switcher
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects and tooltips if needed
    const tenantSwitcher = document.getElementById('tenantSwitcherDropdown');
    if (tenantSwitcher) {
        tenantSwitcher.setAttribute('title', 'Switch between your available tenants');
    }
});
</script>
<?php $__env->stopPush(); ?><?php /**PATH C:\xampp\htdocs\Acounting\github\accounting\resources\views/partial/aside.blade.php ENDPATH**/ ?>