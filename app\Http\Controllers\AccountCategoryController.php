<?php

namespace App\Http\Controllers;

use App\Models\AccountType;
use App\Models\AccountCategory;
use Illuminate\Http\Request;

class AccountCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', AccountCategory::class);

        $search = $request->get('search', '');

        $accountCategories = AccountCategory::search($search)
            ->latest()
            ->paginate()
            ->withQueryString();

        return view('app.account_categories.index', compact('accountCategories', 'search'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', AccountCategory::class);

        $accountTypes = AccountType::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.account_categories.create', compact('accountTypes'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', AccountCategory::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:255|unique:account_categories,code',
            'description' => 'nullable|string',
            'account_type_id' => 'required|tenant_exists:account_types,id',
            'is_active' => 'boolean',
            'is_system' => 'boolean',
        ]);

        $accountCategory = AccountCategory::create($validated);

        return redirect()
            ->route('account-categories.edit', $accountCategory)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\AccountCategory  $accountCategory
     * @return \Illuminate\Http\Response
     */
    public function show(AccountCategory $accountCategory)
    {
        $this->authorize('view', $accountCategory);

        return view('app.account_categories.show', compact('accountCategory'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\AccountCategory  $accountCategory
     * @return \Illuminate\Http\Response
     */
    public function edit(AccountCategory $accountCategory)
    {
        $this->authorize('update', $accountCategory);

        $accountTypes = AccountType::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.account_categories.edit', compact('accountCategory', 'accountTypes'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\AccountCategory  $accountCategory
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, AccountCategory $accountCategory)
    {
        $this->authorize('update', $accountCategory);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:255|unique:account_categories,code,' . $accountCategory->id,
            'description' => 'nullable|string',
            'account_type_id' => 'required|tenant_exists:account_types,id',
            'is_active' => 'boolean',
            'is_system' => 'boolean',
        ]);

        $accountCategory->update($validated);

        return redirect()
            ->route('account-categories.edit', $accountCategory)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\AccountCategory  $accountCategory
     * @return \Illuminate\Http\Response
     */
    public function destroy(AccountCategory $accountCategory)
    {
        $this->authorize('delete', $accountCategory);

        if ($accountCategory->is_system) {
            return redirect()
                ->route('account-categories.index')
                ->withError('System account categories cannot be deleted.');
        }

        if ($accountCategory->accounts()->count() > 0) {
            return redirect()
                ->route('account-categories.index')
                ->withError('Account category is in use and cannot be deleted.');
        }

        $accountCategory->delete();

        return redirect()
            ->route('account-categories.index')
            ->withSuccess(__('crud.common.removed'));
    }
}
