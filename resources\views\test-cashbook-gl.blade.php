<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Cashbook GL Integration Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-calculator me-2"></i>Cashbook GL Integration - Test Page
                        </h4>
                    </div>
                    <div class="card-body">
                        <p class="lead">Test the new General Ledger integration for the Cashbook module.</p>
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-check-circle me-2"></i>GL Integration Features
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item">
                                                <i class="bi bi-journal-check text-primary me-2"></i>
                                                <strong>Automatic Journal Entries:</strong> Every bank transaction creates GL entries
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-arrow-left-right text-success me-2"></i>
                                                <strong>Double Entry Accounting:</strong> Proper debit/credit posting
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-bank text-info me-2"></i>
                                                <strong>Bank Account GL Link:</strong> Bank accounts linked to GL accounts
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-gear text-warning me-2"></i>
                                                <strong>Contra Account Selection:</strong> Manual or automatic contra account
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-arrow-counterclockwise text-danger me-2"></i>
                                                <strong>Reversing Entries:</strong> Automatic reversals on edit/delete
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-calendar text-secondary me-2"></i>
                                                <strong>Fiscal Period Integration:</strong> Entries posted to correct periods
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-diagram-3 me-2"></i>How GL Integration Works
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <h6 class="text-success">
                                                <i class="bi bi-arrow-down-circle me-1"></i>Credit Transaction (Money In)
                                            </h6>
                                            <p class="small text-muted mb-0">
                                                <strong>Debit:</strong> Bank Account (Asset increases)<br>
                                                <strong>Credit:</strong> Revenue/Income Account
                                            </p>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <h6 class="text-danger">
                                                <i class="bi bi-arrow-up-circle me-1"></i>Debit Transaction (Money Out)
                                            </h6>
                                            <p class="small text-muted mb-0">
                                                <strong>Credit:</strong> Bank Account (Asset decreases)<br>
                                                <strong>Debit:</strong> Expense Account
                                            </p>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <h6 class="text-primary">
                                                <i class="bi bi-journal-text me-1"></i>Journal Entry Structure
                                            </h6>
                                            <p class="small text-muted mb-0">
                                                Entry Number: CB-YYYY-XXXXXX<br>
                                                Reference: CASHBOOK-{payment_id}<br>
                                                Status: Posted automatically
                                            </p>
                                        </div>
                                        
                                        <div>
                                            <h6 class="text-warning">
                                                <i class="bi bi-arrow-repeat me-1"></i>Reversing Entries
                                            </h6>
                                            <p class="small text-muted mb-0">
                                                When editing/deleting: Creates REV-{original_entry} with swapped debits/credits
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center mb-4">
                            <a href="{{ route('cashbook.index') }}" class="btn btn-primary btn-lg">
                                <i class="bi bi-bank me-2"></i>Open Cashbook
                            </a>
                            
                            <a href="{{ route('cashbook.create') }}" class="btn btn-success btn-lg">
                                <i class="bi bi-plus-circle me-2"></i>Add GL Transaction
                            </a>
                            
                            @if(Route::has('journal-entries.index'))
                            <a href="{{ route('journal-entries.index') }}" class="btn btn-outline-secondary btn-lg">
                                <i class="bi bi-journal-text me-2"></i>View Journal Entries
                            </a>
                            @endif
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="text-primary">
                                    <i class="bi bi-list-check me-2"></i>GL Integration Test Steps
                                </h5>
                                <ol class="list-group list-group-numbered">
                                    <li class="list-group-item">
                                        <strong>Setup Bank Account:</strong> Ensure bank account is linked to a GL account
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Add Credit Transaction:</strong> Record money coming in (revenue)
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Add Debit Transaction:</strong> Record money going out (expense)
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Check GL Status:</strong> Verify "Posted" status in cashbook
                                    </li>
                                    <li class="list-group-item">
                                        <strong>View Journal Entries:</strong> Check automatic GL postings
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Test Edit/Delete:</strong> Verify reversing entries are created
                                    </li>
                                </ol>
                            </div>
                            
                            <div class="col-md-6">
                                <h5 class="text-success">
                                    <i class="bi bi-database me-2"></i>Database Integration
                                </h5>
                                <div class="bg-light p-3 rounded">
                                    <code class="small">
                                        <strong>Journal Entries:</strong><br>
                                        - entry_number: CB-YYYY-XXXXXX<br>
                                        - reference_number: CASHBOOK-{payment_id}<br>
                                        - status: posted/reversed<br>
                                        - fiscal_year_id, fiscal_period_id<br><br>
                                        
                                        <strong>Journal Entry Lines:</strong><br>
                                        - account_id (bank account GL)<br>
                                        - account_id (contra account)<br>
                                        - debit/credit amounts<br>
                                        - description<br><br>
                                        
                                        <strong>Bank Account:</strong><br>
                                        - gl_account_id (linked GL account)<br>
                                        - current_balance (updated)<br><br>
                                        
                                        <strong>Payment:</strong><br>
                                        - paymentable_type: 'BankAccount'<br>
                                        - amount (+ credit, - debit)
                                    </code>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-4">
                            <h6 class="alert-heading">
                                <i class="bi bi-info-circle me-2"></i>GL Integration Benefits
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li><strong>Automatic Posting:</strong> No manual GL entry required</li>
                                        <li><strong>Audit Trail:</strong> Complete transaction history</li>
                                        <li><strong>Balance Reconciliation:</strong> Bank and GL always in sync</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li><strong>Error Prevention:</strong> Automatic double-entry validation</li>
                                        <li><strong>Reporting:</strong> Integrated financial reports</li>
                                        <li><strong>Compliance:</strong> Proper accounting standards</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning mt-3">
                            <h6 class="alert-heading">
                                <i class="bi bi-exclamation-triangle me-2"></i>Requirements
                            </h6>
                            <p class="mb-0">
                                For GL integration to work properly, ensure:
                                <strong>Bank accounts are linked to GL accounts</strong>, 
                                <strong>Active fiscal year exists</strong>, and 
                                <strong>GL accounts allow manual entries</strong>.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
