

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
          <div class="col-sm mb-2 mb-sm-0">
            <h1 class="page-header-title">
                Edit Currency
                <a href="<?php echo e(route('currencies.index')); ?>" class="mr-4 float-right"
                    ><i class="icon ion-md-arrow-back"></i> Back
                </a>
            </h1>
          </div>
          <!-- End Col -->
        </div>
    </div>
    <!-- End Page Header -->

    <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form','data' => ['method' => 'PUT','action' => ''.e(route('currencies.update', $currency)).'','class' => 'mt-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['method' => 'PUT','action' => ''.e(route('currencies.update', $currency)).'','class' => 'mt-4']); ?>
        <?php echo $__env->make('app.currencies.form-inputs', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <div class="mt-4">
            <a
                href="<?php echo e(route('currencies.index')); ?>"
                class="btn btn-light"
            >
                <i class="icon ion-md-return-left text-primary"></i>
                <?php echo app('translator')->get('crud.common.back'); ?>
            </a>

            <a
                href="<?php echo e(route('currencies.create')); ?>"
                class="btn btn-light"
            >
                <?php echo app('translator')->get('crud.common.create'); ?>
            </a>

            <button type="submit" class="btn btn-primary float-right">
                <?php echo app('translator')->get('crud.common.update'); ?>
            </button>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Acounting\github\accounting\resources\views/app/currencies/edit.blade.php ENDPATH**/ ?>