<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Test Cashbook Module</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-bank me-2"></i>Cashbook Module - Test Page
                        </h4>
                    </div>
                    <div class="card-body">
                        <p class="lead">Test the new Cashbook module for managing bank transactions with debits and credits.</p>
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-check-circle me-2"></i>Features Implemented
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item">
                                                <i class="bi bi-bank text-primary me-2"></i>
                                                <strong>Bank Account Management:</strong> Uses existing BankAccount model
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-plus-minus text-success me-2"></i>
                                                <strong>Credit/Debit System:</strong> Credits = positive, Debits = negative
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-database text-info me-2"></i>
                                                <strong>Polymorphic Storage:</strong> Uses Payment table with BankAccount relationship
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-calculator text-warning me-2"></i>
                                                <strong>Balance Tracking:</strong> Real-time balance updates
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-funnel text-secondary me-2"></i>
                                                <strong>Advanced Filtering:</strong> By bank, date range, search terms
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-pencil text-primary me-2"></i>
                                                <strong>Full CRUD:</strong> Create, Read, Update, Delete transactions
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-gear me-2"></i>How It Works
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <h6 class="text-success">
                                                <i class="bi bi-arrow-down-circle me-1"></i>Credits (Money In)
                                            </h6>
                                            <p class="small text-muted mb-0">
                                                Stored as positive values in the Payment table. Examples: deposits, income, transfers in.
                                            </p>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <h6 class="text-danger">
                                                <i class="bi bi-arrow-up-circle me-1"></i>Debits (Money Out)
                                            </h6>
                                            <p class="small text-muted mb-0">
                                                Stored as negative values in the Payment table. Examples: withdrawals, expenses, transfers out.
                                            </p>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <h6 class="text-primary">
                                                <i class="bi bi-database me-1"></i>Database Structure
                                            </h6>
                                            <p class="small text-muted mb-0">
                                                Uses polymorphic relationship: Payment->paymentable_type = 'BankAccount', paymentable_id = bank_account_id
                                            </p>
                                        </div>
                                        
                                        <div>
                                            <h6 class="text-warning">
                                                <i class="bi bi-calculator me-1"></i>Balance Calculation
                                            </h6>
                                            <p class="small text-muted mb-0">
                                                Current Balance = Opening Balance + Sum(All Transactions)
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <a href="{{ route('cashbook.index') }}" class="btn btn-primary btn-lg">
                                <i class="bi bi-bank me-2"></i>Open Cashbook
                            </a>
                            
                            <a href="{{ route('cashbook.create') }}" class="btn btn-success btn-lg">
                                <i class="bi bi-plus-circle me-2"></i>Add Transaction
                            </a>
                            
                            @if(Route::has('bank-accounts.index'))
                            <a href="{{ route('bank-accounts.index') }}" class="btn btn-outline-secondary btn-lg">
                                <i class="bi bi-bank2 me-2"></i>Manage Banks
                            </a>
                            @endif
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="text-primary">
                                    <i class="bi bi-list-check me-2"></i>Quick Test Steps
                                </h5>
                                <ol class="list-group list-group-numbered">
                                    <li class="list-group-item">
                                        <strong>Create/Check Bank Account:</strong> Ensure you have at least one active bank account
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Add Credit Transaction:</strong> Record money coming into the bank (positive amount)
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Add Debit Transaction:</strong> Record money going out of the bank (negative amount)
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Check Balance:</strong> Verify the running balance updates correctly
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Test Filters:</strong> Filter by bank account, date range, or search terms
                                    </li>
                                </ol>
                            </div>
                            
                            <div class="col-md-6">
                                <h5 class="text-success">
                                    <i class="bi bi-database me-2"></i>Database Schema
                                </h5>
                                <div class="bg-light p-3 rounded">
                                    <code class="small">
                                        <strong>payments table:</strong><br>
                                        - id (primary key)<br>
                                        - amount (decimal: + for credit, - for debit)<br>
                                        - description (transaction description)<br>
                                        - comment (additional notes)<br>
                                        - reference_no (check #, transfer ID, etc.)<br>
                                        - paymentable_id (bank_account.id)<br>
                                        - paymentable_type ('BankAccount')<br>
                                        - currency_id (optional)<br>
                                        - created_at (transaction date)<br>
                                        - created_by, updated_by<br><br>
                                        
                                        <strong>bank_accounts table:</strong><br>
                                        - current_balance (auto-updated)<br>
                                        - opening_balance (starting balance)<br>
                                        - name, account_number, bank_name<br>
                                        - is_active (boolean)
                                    </code>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-4">
                            <h6 class="alert-heading">
                                <i class="bi bi-info-circle me-2"></i>Navigation
                            </h6>
                            <p class="mb-0">
                                The Cashbook module has been added to the <strong>Receivables/Payables</strong> section in the main navigation menu.
                                You can access it from the sidebar under the bank icon.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
