@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-plus-circle me-2"></i>Create Journal Entry
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('journal-entries.index') }}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-arrow-left me-1"></i>Back to Journal Entries
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-journal-text me-2"></i>New Journal Entry
                    </h5>
                </div>

                <form method="POST" action="{{ route('journal-entries.store') }}" id="journalEntryForm">
                    @csrf
                    <div class="card-body">
                        
                        <!-- Error Display -->
                        @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <h6 class="alert-heading">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Please fix the following errors:
                            </h6>
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        @endif

                        <!-- Entry Information -->
                        <h6 class="text-primary mb-3">
                            <i class="bi bi-info-circle me-2"></i>Entry Information
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Entry Date <span class="text-danger">*</span></label>
                                <input type="date" name="entry_date" class="form-control @error('entry_date') is-invalid @enderror" 
                                       value="{{ old('entry_date', date('Y-m-d')) }}" required>
                                @error('entry_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Reference Number</label>
                                <input type="text" name="reference_number" class="form-control @error('reference_number') is-invalid @enderror" 
                                       placeholder="Optional reference" value="{{ old('reference_number') }}">
                                @error('reference_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Fiscal Year <span class="text-danger">*</span></label>
                                <select name="fiscal_year_id" class="form-select @error('fiscal_year_id') is-invalid @enderror" required>
                                    <option value="">Select Fiscal Year</option>
                                    @foreach($fiscalYears as $id => $name)
                                        <option value="{{ $id }}" {{ old('fiscal_year_id') == $id ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('fiscal_year_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Fiscal Period <span class="text-danger">*</span></label>
                                <select name="fiscal_period_id" class="form-select @error('fiscal_period_id') is-invalid @enderror" required>
                                    <option value="">Select Fiscal Period</option>
                                    @foreach($fiscalPeriods as $id => $name)
                                        <option value="{{ $id }}" {{ old('fiscal_period_id') == $id ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('fiscal_period_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Currency</label>
                                <select name="currency_id" class="form-select @error('currency_id') is-invalid @enderror">
                                    <option value="">Default Currency</option>
                                    @foreach($currencies as $id => $name)
                                        <option value="{{ $id }}" {{ old('currency_id') == $id ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('currency_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Exchange Rate</label>
                                <input type="number" name="exchange_rate" class="form-control @error('exchange_rate') is-invalid @enderror" 
                                       step="0.0001" min="0" placeholder="1.0000" value="{{ old('exchange_rate', 1) }}">
                                @error('exchange_rate')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea name="description" class="form-control @error('description') is-invalid @enderror" 
                                      rows="3" placeholder="Journal entry description">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Journal Entry Lines -->
                        <h6 class="text-primary mb-3 mt-4">
                            <i class="bi bi-list-ul me-2"></i>Journal Entry Lines
                        </h6>

                        <div id="journal-lines">
                            <!-- Lines will be added here dynamically -->
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <button type="button" class="btn btn-outline-primary btn-sm" id="add-line">
                                <i class="bi bi-plus-circle me-1"></i>Add Line
                            </button>
                            <div class="text-end">
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Total Debits:</small>
                                        <div class="fw-bold text-success" id="total-debits">$0.00</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Total Credits:</small>
                                        <div class="fw-bold text-danger" id="total-credits">$0.00</div>
                                    </div>
                                </div>
                                <div class="mt-1">
                                    <small class="text-muted">Difference:</small>
                                    <div class="fw-bold" id="difference">$0.00</div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Double-Entry Rule:</strong> Total debits must equal total credits for the entry to be valid.
                        </div>

                    </div>

                    <div class="card-footer d-flex justify-content-between">
                        <a href="{{ route('journal-entries.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary" id="submit-btn" disabled>
                            <i class="bi bi-check-circle me-1"></i>Create Journal Entry
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let lineIndex = 0;
    
    // Add initial two lines
    addLine();
    addLine();
    
    function addLine() {
        const container = document.getElementById('journal-lines');
        const lineHtml = `
            <div class="journal-line border rounded p-3 mb-3" data-index="${lineIndex}">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">Line ${lineIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-line" ${lineIndex < 2 ? 'style="display:none"' : ''}>
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Account <span class="text-danger">*</span></label>
                        <select name="lines[${lineIndex}][account_id]" class="form-select account-select" required>
                            <option value="">Select Account</option>
                            @foreach($accounts as $id => $name)
                                <option value="{{ $id }}">{{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Description</label>
                        <input type="text" name="lines[${lineIndex}][description]" class="form-control" placeholder="Line description">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="form-label">Debit</label>
                        <input type="number" name="lines[${lineIndex}][debit]" class="form-control debit-input" step="0.01" min="0" placeholder="0.00">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="form-label">Credit</label>
                        <input type="number" name="lines[${lineIndex}][credit]" class="form-control credit-input" step="0.01" min="0" placeholder="0.00">
                    </div>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', lineHtml);
        lineIndex++;
        updateLineNumbers();
        attachLineEvents();
    }
    
    function updateLineNumbers() {
        const lines = document.querySelectorAll('.journal-line');
        lines.forEach((line, index) => {
            line.querySelector('h6').textContent = `Line ${index + 1}`;
        });
    }
    
    function attachLineEvents() {
        // Remove line events
        document.querySelectorAll('.remove-line').forEach(btn => {
            btn.addEventListener('click', function() {
                if (document.querySelectorAll('.journal-line').length > 2) {
                    this.closest('.journal-line').remove();
                    updateLineNumbers();
                    calculateTotals();
                }
            });
        });
        
        // Amount input events
        document.querySelectorAll('.debit-input, .credit-input').forEach(input => {
            input.addEventListener('input', function() {
                const line = this.closest('.journal-line');
                const debitInput = line.querySelector('.debit-input');
                const creditInput = line.querySelector('.credit-input');
                
                // Clear the other input when one is filled
                if (this.classList.contains('debit-input') && this.value) {
                    creditInput.value = '';
                } else if (this.classList.contains('credit-input') && this.value) {
                    debitInput.value = '';
                }
                
                calculateTotals();
            });
        });
    }
    
    function calculateTotals() {
        let totalDebits = 0;
        let totalCredits = 0;
        
        document.querySelectorAll('.debit-input').forEach(input => {
            totalDebits += parseFloat(input.value) || 0;
        });
        
        document.querySelectorAll('.credit-input').forEach(input => {
            totalCredits += parseFloat(input.value) || 0;
        });
        
        const difference = Math.abs(totalDebits - totalCredits);
        
        document.getElementById('total-debits').textContent = '$' + totalDebits.toFixed(2);
        document.getElementById('total-credits').textContent = '$' + totalCredits.toFixed(2);
        document.getElementById('difference').textContent = '$' + difference.toFixed(2);
        
        // Enable/disable submit button
        const submitBtn = document.getElementById('submit-btn');
        const isBalanced = difference < 0.01 && totalDebits > 0 && totalCredits > 0;
        submitBtn.disabled = !isBalanced;
        
        // Update difference color
        const diffElement = document.getElementById('difference');
        if (isBalanced) {
            diffElement.className = 'fw-bold text-success';
        } else {
            diffElement.className = 'fw-bold text-danger';
        }
    }
    
    // Add line button
    document.getElementById('add-line').addEventListener('click', addLine);
    
    // Initial calculation
    calculateTotals();
});
</script>
@endpush
