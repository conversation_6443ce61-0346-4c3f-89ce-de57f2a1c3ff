@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-calendar-range me-2"></i>{{ $fiscalYear->name }}
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group" role="group">
                <a href="{{ route('fiscal-years.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Fiscal Years
                </a>
                @can('update', $fiscalYear)
                <a href="{{ route('fiscal-years.edit', $fiscalYear) }}" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-pencil me-1"></i>Edit
                </a>
                @endcan
                @can('create', App\Models\FiscalYear::class)
                <a href="{{ route('fiscal-years.create') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>New Fiscal Year
                </a>
                @endcan
                <button type="button" class="btn btn-outline-info btn-sm" onclick="window.print()">
                    <i class="bi bi-printer me-1"></i>Print
                </button>
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <!-- Fiscal Year Overview -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Fiscal Year Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ $fiscalYear->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Start Date:</strong></td>
                                    <td>{{ $fiscalYear->start_date->format('M d, Y') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>End Date:</strong></td>
                                    <td>{{ $fiscalYear->end_date->format('M d, Y') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Duration:</strong></td>
                                    <td>
                                        @php
                                            $duration = $fiscalYear->start_date->diffInDays($fiscalYear->end_date) + 1;
                                            $months = round($duration / 30.44);
                                        @endphp
                                        {{ $duration }} days ({{ $months }} months)
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @switch($fiscalYear->status)
                                            @case('open')
                                                <span class="badge bg-success">Open</span>
                                                @break
                                            @case('closed')
                                                <span class="badge bg-danger">Closed</span>
                                                @break
                                            @case('locked')
                                                <span class="badge bg-warning">Locked</span>
                                                @break
                                            @default
                                                <span class="badge bg-secondary">{{ ucfirst($fiscalYear->status) }}</span>
                                        @endswitch
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Active:</strong></td>
                                    <td>
                                        @if($fiscalYear->is_active)
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>Active
                                            </span>
                                        @else
                                            <span class="badge bg-secondary">
                                                <i class="bi bi-x-circle me-1"></i>Inactive
                                            </span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>{{ $fiscalYear->created_at->format('M d, Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Updated:</strong></td>
                                    <td>{{ $fiscalYear->updated_at->format('M d, Y H:i') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($fiscalYear->description)
                    <div class="mt-3">
                        <h6>Description:</h6>
                        <p class="text-muted">{{ $fiscalYear->description }}</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Fiscal Year Actions -->
        <div class="col-lg-4">
            <!-- Status Card -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-gear me-2"></i>Year Status
                    </h6>
                </div>
                <div class="card-body text-center">
                    @switch($fiscalYear->status)
                        @case('open')
                            <i class="bi bi-unlock display-4 text-success d-block mb-2"></i>
                            <h5 class="text-success">Open</h5>
                            <p class="text-muted small">Fiscal year is open for transactions.</p>
                            @break
                        @case('closed')
                            <i class="bi bi-lock display-4 text-danger d-block mb-2"></i>
                            <h5 class="text-danger">Closed</h5>
                            <p class="text-muted small">Fiscal year is closed. No new transactions allowed.</p>
                            @break
                        @case('locked')
                            <i class="bi bi-shield-lock display-4 text-warning d-block mb-2"></i>
                            <h5 class="text-warning">Locked</h5>
                            <p class="text-muted small">Fiscal year is locked for audit purposes.</p>
                            @break
                    @endswitch
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-lightning me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @can('update', $fiscalYear)
                        <a href="{{ route('fiscal-years.edit', $fiscalYear) }}" class="btn btn-primary btn-sm">
                            <i class="bi bi-pencil me-1"></i>Edit Fiscal Year
                        </a>

                        @if($fiscalYear->status === 'open')
                        <form action="{{ route('fiscal-years.close', $fiscalYear) }}" method="POST"
                              onsubmit="return confirm('Are you sure you want to close this fiscal year? This action cannot be undone.')">
                            @csrf
                            <button type="submit" class="btn btn-warning btn-sm w-100">
                                <i class="bi bi-lock me-1"></i>Close Fiscal Year
                            </button>
                        </form>
                        @endif
                        @endcan

                        <a href="{{ route('fiscal-periods.index') }}?fiscal_year_id={{ $fiscalYear->id }}" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-calendar3 me-1"></i>View Periods
                        </a>

                        <button type="button" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-download me-1"></i>Export Data
                        </button>

                        @can('delete', $fiscalYear)
                        @if($fiscalYear->status === 'open' && !$fiscalYear->is_active)
                        <form method="POST" action="{{ route('fiscal-years.destroy', $fiscalYear) }}"
                              onsubmit="return confirm('Are you sure you want to delete this fiscal year?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                <i class="bi bi-trash me-1"></i>Delete Year
                            </button>
                        </form>
                        @endif
                        @endcan
                    </div>
                </div>
            </div>

            <!-- Year Statistics -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-bar-chart me-2"></i>Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary mb-1">{{ $fiscalYear->fiscalPeriods->count() ?? 0 }}</h4>
                                <small class="text-muted">Periods</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success mb-1">{{ $fiscalYear->journalEntries->count() ?? 0 }}</h4>
                            <small class="text-muted">Entries</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Fiscal Periods -->
    @can('view-any', App\Models\FiscalPeriod::class)
    <div class="card mt-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-calendar3 me-2"></i>Fiscal Periods
            </h5>
            <span class="badge bg-secondary">{{ $fiscalYear->fiscalPeriods->count() ?? 0 }} periods</span>
        </div>
        <div class="card-body">
            @if($fiscalYear->fiscalPeriods && $fiscalYear->fiscalPeriods->count() > 0)
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Period Name</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th class="text-center">Status</th>
                            <th class="text-center">Active</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($fiscalYear->fiscalPeriods as $period)
                        <tr>
                            <td>
                                <strong>{{ $period->name }}</strong>
                                @if($period->description)
                                    <br><small class="text-muted">{{ $period->description }}</small>
                                @endif
                            </td>
                            <td>{{ $period->start_date->format('M d, Y') }}</td>
                            <td>{{ $period->end_date->format('M d, Y') }}</td>
                            <td class="text-center">
                                @switch($period->status)
                                    @case('open')
                                        <span class="badge bg-success">Open</span>
                                        @break
                                    @case('closed')
                                        <span class="badge bg-danger">Closed</span>
                                        @break
                                    @default
                                        <span class="badge bg-secondary">{{ ucfirst($period->status) }}</span>
                                @endswitch
                            </td>
                            <td class="text-center">
                                @if($period->is_active)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-secondary">Inactive</span>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            @else
            <div class="text-center py-4">
                <i class="bi bi-calendar3 display-4 text-muted d-block mb-2"></i>
                <p class="text-muted mb-0">No fiscal periods found for this year.</p>
                @can('create', App\Models\FiscalPeriod::class)
                <a href="{{ route('fiscal-periods.create') }}?fiscal_year_id={{ $fiscalYear->id }}" class="btn btn-primary btn-sm mt-2">
                    <i class="bi bi-plus-circle me-1"></i>Create Periods
                </a>
                @endcan
            </div>
            @endif
        </div>
    </div>
    @endcan
</div>

@if(session('success'))
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            {{ session('success') }}
        </div>
    </div>
</div>
@endif

@endsection

@push('styles')
<style>
@media print {
    .btn, .card-header .btn-group {
        display: none !important;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
@endpush
