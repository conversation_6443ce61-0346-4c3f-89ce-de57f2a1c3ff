@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-bank2 me-2"></i>Bank Accounts
        </x-slot>
        <x-slot name="controls">
            @can('create', App\Models\BankAccount::class)
            <div class="btn-group" role="group">
                <a href="{{ route('bank-accounts.create') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>Add Bank Account
                </a>
                <a href="{{ route('cashbook.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-bank me-1"></i>View Cashbook
                </a>
            </div>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-funnel me-2"></i>Filters
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('bank-accounts.index') }}">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">Search</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="Search bank name, account number..." value="{{ $search }}">
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="is_active" class="form-select">
                            <option value="">All Accounts</option>
                            <option value="1" {{ $isActive === '1' ? 'selected' : '' }}>Active</option>
                            <option value="0" {{ $isActive === '0' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>Filter
                        </button>
                        <a href="{{ route('bank-accounts.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bank Accounts Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>Bank Accounts
            </h5>
            <span class="badge bg-secondary">{{ $bankAccounts->total() }} accounts</span>
        </div>
        
        <div class="table-responsive">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>Bank Details</th>
                        <th>Account Info</th>
                        <th class="text-end">Opening Balance</th>
                        <th class="text-end">Current Balance</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($bankAccounts as $account)
                    <tr>
                        <td>
                            <div>
                                <strong>{{ $account->name }}</strong>
                                <br>
                                <small class="text-muted">{{ $account->bank_name }}</small>
                                @if($account->branch_name)
                                    <br><small class="text-muted">{{ $account->branch_name }}</small>
                                @endif
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>{{ $account->account_number }}</strong>
                                <br>
                                <small class="text-muted">{{ ucfirst($account->account_type) }}</small>
                                @if($account->swift_code)
                                    <br><small class="text-muted">SWIFT: {{ $account->swift_code }}</small>
                                @endif
                            </div>
                        </td>
                        <td class="text-end">
                            <span class="fw-bold">
                                {{ $account->currency->symbol ?? '$' }}{{ number_format($account->opening_balance, 2) }}
                            </span>
                            @if($account->opening_balance_date)
                                <br><small class="text-muted">{{ $account->opening_balance_date->format('M d, Y') }}</small>
                            @endif
                        </td>
                        <td class="text-end">
                            <span class="fw-bold {{ $account->current_balance >= 0 ? 'text-success' : 'text-danger' }}">
                                {{ $account->currency->symbol ?? '$' }}{{ number_format($account->current_balance, 2) }}
                            </span>
                            <br>
                            <a href="{{ route('cashbook.index', ['bank_account_id' => $account->id]) }}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-eye me-1"></i>View Transactions
                            </a>
                        </td>
                        <td class="text-center">
                            @if($account->is_active)
                                <span class="badge bg-success">Active</span>
                            @else
                                <span class="badge bg-secondary">Inactive</span>
                            @endif
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                @can('view', $account)
                                <a href="{{ route('bank-accounts.show', $account) }}" 
                                   class="btn btn-sm btn-outline-info" title="View Details">
                                    <i class="bi bi-eye"></i>
                                </a>
                                @endcan
                                
                                @can('update', $account)
                                <a href="{{ route('bank-accounts.edit', $account) }}" 
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                @endcan
                                
                                <a href="{{ route('cashbook.create', ['bank_account_id' => $account->id]) }}" 
                                   class="btn btn-sm btn-outline-success" title="Add Transaction">
                                    <i class="bi bi-plus-circle"></i>
                                </a>
                                
                                @can('delete', $account)
                                <form method="POST" action="{{ route('bank-accounts.destroy', $account) }}" 
                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this bank account?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-bank2 display-4 d-block mb-2"></i>
                                <p class="mb-0">No bank accounts found.</p>
                                @can('create', App\Models\BankAccount::class)
                                <a href="{{ route('bank-accounts.create') }}" class="btn btn-primary btn-sm mt-2">
                                    <i class="bi bi-plus-circle me-1"></i>Add First Bank Account
                                </a>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($bankAccounts->hasPages())
        <div class="card-footer">
            {{ $bankAccounts->appends(request()->query())->links() }}
        </div>
        @endif
    </div>

    <!-- Summary Cards -->
    @if($bankAccounts->count() > 0)
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $bankAccounts->where('is_active', true)->count() }}</h3>
                    <small>Active Accounts</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">${{ number_format($bankAccounts->sum('current_balance'), 2) }}</h3>
                    <small>Total Balance</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">${{ number_format($bankAccounts->sum('opening_balance'), 2) }}</h3>
                    <small>Total Opening Balance</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">${{ number_format($bankAccounts->sum('current_balance') - $bankAccounts->sum('opening_balance'), 2) }}</h3>
                    <small>Net Movement</small>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

@if(session('success'))
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            {{ session('success') }}
        </div>
    </div>
</div>
@endif

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
@endpush
