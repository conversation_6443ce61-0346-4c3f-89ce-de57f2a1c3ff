<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;

class Transaction extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BusinessTypeTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'account_id',
        'description',
        'reference_no',
        'debit',
        'credit',
        'balance',
        'transaction_date',
        'created_by',
        'updated_by',
        'business_type_id',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'debit' => 'decimal:2',
        'credit' => 'decimal:2',
        'balance' => 'decimal:2',
        'transaction_date' => 'date',
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the transaction amount (debit or credit)
     */
    public function getAmountAttribute()
    {
        return $this->debit ?: $this->credit;
    }

    /**
     * Get the transaction type
     */
    public function getTypeAttribute()
    {
        return $this->debit > 0 ? 'debit' : 'credit';
    }

    /**
     * Check if this is a debit transaction
     */
    public function isDebit()
    {
        return $this->debit > 0;
    }

    /**
     * Check if this is a credit transaction
     */
    public function isCredit()
    {
        return $this->credit > 0;
    }

    /**
     * Get formatted amount for display
     */
    public function getFormattedAmountAttribute()
    {
        $amount = $this->amount;
        return '$' . number_format($amount, 2);
    }

    /**
     * Scope for debit transactions
     */
    public function scopeDebits($query)
    {
        return $query->where('debit', '>', 0);
    }

    /**
     * Scope for credit transactions
     */
    public function scopeCredits($query)
    {
        return $query->where('credit', '>', 0);
    }

    /**
     * Scope for specific account
     */
    public function scopeForAccount($query, $accountId)
    {
        return $query->where('account_id', $accountId);
    }
}
