<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707 = $component; } ?>
<?php $component = App\View\Components\PageHeader::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\PageHeader::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('title', null, []); ?> 
            <i class="bi bi-bank2 me-2"></i>Bank Accounts
         <?php $__env->endSlot(); ?>
         <?php $__env->slot('controls', null, []); ?> 
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\BankAccount::class)): ?>
            <div class="btn-group" role="group">
                <a href="<?php echo e(route('bank-accounts.create')); ?>" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>Add Bank Account
                </a>
                <a href="<?php echo e(route('cashbook.index')); ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-bank me-1"></i>View Cashbook
                </a>
            </div>
            <?php endif; ?>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707)): ?>
<?php $component = $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707; ?>
<?php unset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707); ?>
<?php endif; ?>
    <!-- End Page Header -->

    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-funnel me-2"></i>Filters
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('bank-accounts.index')); ?>">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">Search</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="Search bank name, account number..." value="<?php echo e($search); ?>">
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="is_active" class="form-select">
                            <option value="">All Accounts</option>
                            <option value="1" <?php echo e($isActive === '1' ? 'selected' : ''); ?>>Active</option>
                            <option value="0" <?php echo e($isActive === '0' ? 'selected' : ''); ?>>Inactive</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>Filter
                        </button>
                        <a href="<?php echo e(route('bank-accounts.index')); ?>" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bank Accounts Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>Bank Accounts
            </h5>
            <span class="badge bg-secondary"><?php echo e($bankAccounts->total()); ?> accounts</span>
        </div>
        
        <div class="table-responsive">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>Bank Details</th>
                        <th>Account Info</th>
                        <th class="text-end">Opening Balance</th>
                        <th class="text-end">Current Balance</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $bankAccounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td>
                            <div>
                                <strong><?php echo e($account->name); ?></strong>
                                <br>
                                <small class="text-muted"><?php echo e($account->bank_name); ?></small>
                                <?php if($account->branch_name): ?>
                                    <br><small class="text-muted"><?php echo e($account->branch_name); ?></small>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong><?php echo e($account->account_number); ?></strong>
                                <br>
                                <small class="text-muted"><?php echo e(ucfirst($account->account_type)); ?></small>
                                <?php if($account->swift_code): ?>
                                    <br><small class="text-muted">SWIFT: <?php echo e($account->swift_code); ?></small>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="text-end">
                            <span class="fw-bold">
                                <?php echo e($account->currency->symbol ?? '$'); ?><?php echo e(number_format($account->opening_balance, 2)); ?>

                            </span>
                            <?php if($account->opening_balance_date): ?>
                                <br><small class="text-muted"><?php echo e($account->opening_balance_date->format('M d, Y')); ?></small>
                            <?php endif; ?>
                        </td>
                        <td class="text-end">
                            <span class="fw-bold <?php echo e($account->current_balance >= 0 ? 'text-success' : 'text-danger'); ?>">
                                <?php echo e($account->currency->symbol ?? '$'); ?><?php echo e(number_format($account->current_balance, 2)); ?>

                            </span>
                            <br>
                            <a href="<?php echo e(route('cashbook.index', ['bank_account_id' => $account->id])); ?>" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-eye me-1"></i>View Transactions
                            </a>
                        </td>
                        <td class="text-center">
                            <?php if($account->is_active): ?>
                                <span class="badge bg-success">Active</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Inactive</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $account)): ?>
                                <a href="<?php echo e(route('bank-accounts.show', $account)); ?>" 
                                   class="btn btn-sm btn-outline-info" title="View Details">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <?php endif; ?>
                                
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $account)): ?>
                                <a href="<?php echo e(route('bank-accounts.edit', $account)); ?>" 
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <?php endif; ?>
                                
                                <a href="<?php echo e(route('cashbook.create', ['bank_account_id' => $account->id])); ?>" 
                                   class="btn btn-sm btn-outline-success" title="Add Transaction">
                                    <i class="bi bi-plus-circle"></i>
                                </a>
                                
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $account)): ?>
                                <form method="POST" action="<?php echo e(route('bank-accounts.destroy', $account)); ?>" 
                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this bank account?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-bank2 display-4 d-block mb-2"></i>
                                <p class="mb-0">No bank accounts found.</p>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\BankAccount::class)): ?>
                                <a href="<?php echo e(route('bank-accounts.create')); ?>" class="btn btn-primary btn-sm mt-2">
                                    <i class="bi bi-plus-circle me-1"></i>Add First Bank Account
                                </a>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if($bankAccounts->hasPages()): ?>
        <div class="card-footer">
            <?php echo e($bankAccounts->appends(request()->query())->links()); ?>

        </div>
        <?php endif; ?>
    </div>

    <!-- Summary Cards -->
    <?php if($bankAccounts->count() > 0): ?>
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1"><?php echo e($bankAccounts->where('is_active', true)->count()); ?></h3>
                    <small>Active Accounts</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">$<?php echo e(number_format($bankAccounts->sum('current_balance'), 2)); ?></h3>
                    <small>Total Balance</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">$<?php echo e(number_format($bankAccounts->sum('opening_balance'), 2)); ?></h3>
                    <small>Total Opening Balance</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">$<?php echo e(number_format($bankAccounts->sum('current_balance') - $bankAccounts->sum('opening_balance'), 2)); ?></h3>
                    <small>Net Movement</small>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php if(session('success')): ?>
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            <?php echo e(session('success')); ?>

        </div>
    </div>
</div>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Acounting\github\accounting\resources\views/app/bank_accounts/index.blade.php ENDPATH**/ ?>