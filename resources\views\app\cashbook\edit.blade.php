@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-pencil me-2"></i>Edit Bank Transaction
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('cashbook.index') }}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-arrow-left me-1"></i>Back to Cashbook
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bank me-2"></i>Edit Transaction #{{ $payment->id }}
                    </h5>
                </div>

                <form method="POST" action="{{ route('cashbook.update', $payment) }}" id="transactionForm">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        
                        <!-- Error Display -->
                        @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <h6 class="alert-heading">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Please fix the following errors:
                            </h6>
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        @endif

                        <div class="row">
                            <!-- Bank Account Selection -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Bank Account <span class="text-danger">*</span></label>
                                <select name="bank_account_id" class="form-select @error('bank_account_id') is-invalid @enderror" 
                                        id="bankAccountSelect" required>
                                    <option value="">Select Bank Account</option>
                                    @foreach($bankAccounts as $account)
                                        <option value="{{ $account->id }}" 
                                            {{ (old('bank_account_id', $payment->paymentable_id) == $account->id) ? 'selected' : '' }}
                                            data-balance="{{ $account->current_balance }}"
                                            data-currency="{{ $account->currency->symbol ?? '$' }}">
                                            {{ $account->name }} ({{ $account->account_number }})
                                        </option>
                                    @endforeach
                                </select>
                                @error('bank_account_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Transaction Type -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Transaction Type <span class="text-danger">*</span></label>
                                <select name="transaction_type" class="form-select @error('transaction_type') is-invalid @enderror" 
                                        id="transactionType" required>
                                    <option value="">Select Type</option>
                                    <option value="credit" 
                                        {{ old('transaction_type', $payment->amount > 0 ? 'credit' : 'debit') == 'credit' ? 'selected' : '' }}>
                                        Credit (Money In) 💰
                                    </option>
                                    <option value="debit" 
                                        {{ old('transaction_type', $payment->amount > 0 ? 'credit' : 'debit') == 'debit' ? 'selected' : '' }}>
                                        Debit (Money Out) 💸
                                    </option>
                                </select>
                                @error('transaction_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <!-- Amount -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Amount <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text" id="currencySymbol">$</span>
                                    <input type="number" name="amount" class="form-control @error('amount') is-invalid @enderror" 
                                           step="0.01" min="0.01" placeholder="0.00" 
                                           value="{{ old('amount', abs($payment->amount)) }}" required>
                                </div>
                                @error('amount')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Transaction Date -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Transaction Date <span class="text-danger">*</span></label>
                                <input type="date" name="transaction_date" 
                                       class="form-control @error('transaction_date') is-invalid @enderror" 
                                       value="{{ old('transaction_date', $payment->created_at->format('Y-m-d')) }}" required>
                                @error('transaction_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label class="form-label">Description <span class="text-danger">*</span></label>
                            <input type="text" name="description" 
                                   class="form-control @error('description') is-invalid @enderror" 
                                   placeholder="Enter transaction description" 
                                   value="{{ old('description', $payment->description) }}" required>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <!-- Reference Number -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Reference Number</label>
                                <input type="text" name="reference_no" 
                                       class="form-control @error('reference_no') is-invalid @enderror" 
                                       placeholder="Check number, transfer ID, etc." 
                                       value="{{ old('reference_no', $payment->reference_no) }}">
                                @error('reference_no')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Currency -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Currency</label>
                                <select name="currency_id" class="form-select @error('currency_id') is-invalid @enderror">
                                    <option value="">Default Currency</option>
                                    @foreach($currencies as $currency)
                                        <option value="{{ $currency->id }}" 
                                            {{ old('currency_id', $payment->currency_id) == $currency->id ? 'selected' : '' }}>
                                            {{ $currency->code }} - {{ $currency->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('currency_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Comment -->
                        <div class="mb-3">
                            <label class="form-label">Additional Notes</label>
                            <textarea name="comment" class="form-control @error('comment') is-invalid @enderror" 
                                      rows="3" placeholder="Any additional notes or comments">{{ old('comment', $payment->comment) }}</textarea>
                            @error('comment')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Current Balance Display -->
                        <div id="balanceDisplay" class="alert alert-info" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center">
                                <span><strong>Current Balance:</strong></span>
                                <span id="currentBalance" class="fw-bold"></span>
                            </div>
                        </div>

                        <!-- Transaction Info -->
                        <div class="alert alert-light">
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong>Created:</strong> {{ $payment->created_at->format('M d, Y H:i') }}
                                        @if($payment->createdBy)
                                            by {{ $payment->createdBy->name }}
                                        @endif
                                    </small>
                                </div>
                                @if($payment->updated_at != $payment->created_at)
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong>Last Updated:</strong> {{ $payment->updated_at->format('M d, Y H:i') }}
                                        @if($payment->updatedBy)
                                            by {{ $payment->updatedBy->name }}
                                        @endif
                                    </small>
                                </div>
                                @endif
                            </div>
                        </div>

                    </div>

                    <div class="card-footer d-flex justify-content-between">
                        <a href="{{ route('cashbook.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>Update Transaction
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const bankAccountSelect = document.getElementById('bankAccountSelect');
    const transactionType = document.getElementById('transactionType');
    const currencySymbol = document.getElementById('currencySymbol');
    const balanceDisplay = document.getElementById('balanceDisplay');
    const currentBalance = document.getElementById('currentBalance');

    // Update currency symbol and balance when bank account changes
    bankAccountSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        
        if (selectedOption.value) {
            const balance = selectedOption.dataset.balance;
            const currency = selectedOption.dataset.currency;
            
            currencySymbol.textContent = currency;
            currentBalance.textContent = currency + parseFloat(balance).toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
            balanceDisplay.style.display = 'block';
        } else {
            balanceDisplay.style.display = 'none';
            currencySymbol.textContent = '$';
        }
    });

    // Add visual feedback for transaction type
    transactionType.addEventListener('change', function() {
        const form = document.getElementById('transactionForm');
        form.classList.remove('border-success', 'border-danger');
        
        if (this.value === 'credit') {
            form.classList.add('border-success');
        } else if (this.value === 'debit') {
            form.classList.add('border-danger');
        }
    });

    // Trigger initial updates
    if (bankAccountSelect.value) {
        bankAccountSelect.dispatchEvent(new Event('change'));
    }
    if (transactionType.value) {
        transactionType.dispatchEvent(new Event('change'));
    }
});
</script>
@endpush
