<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707 = $component; } ?>
<?php $component = App\View\Components\PageHeader::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\PageHeader::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('title', null, []); ?> 
            <i class="bi bi-journal-text me-2"></i>Journal Entries
         <?php $__env->endSlot(); ?>
         <?php $__env->slot('controls', null, []); ?> 
            <div class="btn-group" role="group">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\JournalEntry::class)): ?>
                <a href="<?php echo e(route('journal-entries.create')); ?>" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>Add Entry
                </a>
                <?php endif; ?>
                <button type="button" class="btn btn-outline-info btn-sm" onclick="window.print()">
                    <i class="bi bi-printer me-1"></i>Print
                </button>
            </div>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707)): ?>
<?php $component = $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707; ?>
<?php unset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707); ?>
<?php endif; ?>
    <!-- End Page Header -->

    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-funnel me-2"></i>Filters & Search
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('journal-entries.index')); ?>">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Search</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="Search entries..." value="<?php echo e($search); ?>">
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="draft" <?php echo e($status == 'draft' ? 'selected' : ''); ?>>Draft</option>
                            <option value="approved" <?php echo e($status == 'approved' ? 'selected' : ''); ?>>Approved</option>
                            <option value="posted" <?php echo e($status == 'posted' ? 'selected' : ''); ?>>Posted</option>
                            <option value="reversed" <?php echo e($status == 'reversed' ? 'selected' : ''); ?>>Reversed</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">From Date</label>
                        <input type="date" name="from_date" class="form-control" value="<?php echo e($fromDate); ?>">
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">To Date</label>
                        <input type="date" name="to_date" class="form-control" value="<?php echo e($toDate); ?>">
                    </div>
                    
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>Filter
                        </button>
                        <a href="<?php echo e(route('journal-entries.index')); ?>" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Journal Entries Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>Journal Entries
            </h5>
            <span class="badge bg-secondary"><?php echo e($journalEntries->total()); ?> entries</span>
        </div>
        
        <div class="table-responsive">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>Entry Number</th>
                        <th>Date</th>
                        <th>Description</th>
                        <th>Reference</th>
                        <th class="text-end">Total Debit</th>
                        <th class="text-end">Total Credit</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $journalEntries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $entry): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td>
                            <div>
                                <strong>
                                    <a href="<?php echo e(route('journal-entries.show', $entry)); ?>" class="text-decoration-none">
                                        <?php echo e($entry->entry_number); ?>

                                    </a>
                                </strong>
                                <br>
                                <small class="text-muted"><?php echo e($entry->entry_type); ?></small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <?php echo e($entry->entry_date->format('M d, Y')); ?>

                                <br>
                                <small class="text-muted"><?php echo e($entry->created_at->format('H:i')); ?></small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <?php echo e($entry->description ?: 'No description'); ?>

                                <?php if($entry->fiscalYear): ?>
                                    <br><small class="text-info">FY: <?php echo e($entry->fiscalYear->name); ?></small>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td><?php echo e($entry->reference_number ?: '-'); ?></td>
                        <td class="text-end">
                            <span class="fw-bold text-success">
                                $<?php echo e(number_format($entry->journalEntryLines->sum('debit'), 2)); ?>

                            </span>
                        </td>
                        <td class="text-end">
                            <span class="fw-bold text-danger">
                                $<?php echo e(number_format($entry->journalEntryLines->sum('credit'), 2)); ?>

                            </span>
                        </td>
                        <td class="text-center">
                            <?php switch($entry->status):
                                case ('draft'): ?>
                                    <span class="badge bg-secondary">Draft</span>
                                    <?php break; ?>
                                <?php case ('approved'): ?>
                                    <span class="badge bg-warning">Approved</span>
                                    <?php break; ?>
                                <?php case ('posted'): ?>
                                    <span class="badge bg-success">Posted</span>
                                    <?php break; ?>
                                <?php case ('reversed'): ?>
                                    <span class="badge bg-danger">Reversed</span>
                                    <?php break; ?>
                                <?php default: ?>
                                    <span class="badge bg-light text-dark"><?php echo e(ucfirst($entry->status)); ?></span>
                            <?php endswitch; ?>
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $entry)): ?>
                                <a href="<?php echo e(route('journal-entries.show', $entry)); ?>" 
                                   class="btn btn-sm btn-outline-info" title="View Details">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <?php endif; ?>
                                
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $entry)): ?>
                                <?php if($entry->status === 'draft'): ?>
                                <a href="<?php echo e(route('journal-entries.edit', $entry)); ?>" 
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <?php endif; ?>
                                <?php endif; ?>
                                
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $entry)): ?>
                                <?php if($entry->status === 'draft'): ?>
                                <form method="POST" action="<?php echo e(route('journal-entries.destroy', $entry)); ?>" 
                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this journal entry?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-journal-text display-4 d-block mb-2"></i>
                                <p class="mb-0">No journal entries found.</p>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\JournalEntry::class)): ?>
                                <a href="<?php echo e(route('journal-entries.create')); ?>" class="btn btn-primary btn-sm mt-2">
                                    <i class="bi bi-plus-circle me-1"></i>Create First Entry
                                </a>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if($journalEntries->hasPages()): ?>
        <div class="card-footer">
            <?php echo e($journalEntries->appends(request()->query())->links()); ?>

        </div>
        <?php endif; ?>
    </div>

    <!-- Summary Cards -->
    <?php if($journalEntries->count() > 0): ?>
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1"><?php echo e($journalEntries->where('status', 'posted')->count()); ?></h3>
                    <small>Posted Entries</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1"><?php echo e($journalEntries->where('status', 'draft')->count()); ?></h3>
                    <small>Draft Entries</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">$<?php echo e(number_format($journalEntries->flatMap->journalEntryLines->sum('debit'), 2)); ?></h3>
                    <small>Total Debits</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">$<?php echo e(number_format($journalEntries->flatMap->journalEntryLines->sum('credit'), 2)); ?></h3>
                    <small>Total Credits</small>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php if(session('success')): ?>
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            <?php echo e(session('success')); ?>

        </div>
    </div>
</div>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Acounting\github\accounting\resources\views/app/journal_entries/index.blade.php ENDPATH**/ ?>