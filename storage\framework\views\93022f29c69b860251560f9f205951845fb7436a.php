

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707 = $component; } ?>
<?php $component = App\View\Components\PageHeader::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\PageHeader::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('icon', null, []); ?> pencil-square <?php $__env->endSlot(); ?>
         <?php $__env->slot('title', null, []); ?> Edit Account Type: <?php echo e($accountType->name); ?> <?php $__env->endSlot(); ?>
         <?php $__env->slot('subtitle', null, []); ?> Update account type information <?php $__env->endSlot(); ?>
         <?php $__env->slot('breadcrumbs', null, []); ?> 
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Accounting</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('account-types.index')); ?>">Account Types</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit</li>
         <?php $__env->endSlot(); ?>
         <?php $__env->slot('controls', null, []); ?> 
            <a href="<?php echo e(route('account-types.show', $accountType)); ?>" class="btn btn-outline-info me-2">
                <i class="bi bi-eye me-1"></i> View Account Type
            </a>
            <a href="<?php echo e(route('account-types.index')); ?>" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Account Types
            </a>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707)): ?>
<?php $component = $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707; ?>
<?php unset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707); ?>
<?php endif; ?>
    <!-- End Page Header -->

    <!-- Form Card -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-header-title">Account Type Information</h5>
                <span class="badge bg-primary">ID: <?php echo e($accountType->id); ?></span>
            </div>
        </div>
        <div class="card-body">
            <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form','data' => ['method' => 'PUT','action' => ''.e(route('account-types.update', $accountType)).'','class' => 'mt-2']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['method' => 'PUT','action' => ''.e(route('account-types.update', $accountType)).'','class' => 'mt-2']); ?>
                <?php echo $__env->make('app.account_types.form-inputs', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                <div class="d-flex justify-content-between mt-4 border-top pt-4">
                    <div>
                        <a href="<?php echo e(route('account-types.index')); ?>" class="btn btn-outline-secondary me-2">
                            <i class="bi bi-arrow-left me-1"></i> Back
                        </a>

                        <a href="<?php echo e(route('account-types.create')); ?>" class="btn btn-outline-primary">
                            <i class="bi bi-plus-circle me-1"></i> New Account Type
                        </a>
                    </div>

                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-1"></i> Save Changes
                    </button>
                </div>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Acounting\github\accounting\resources\views/app/account_types/edit.blade.php ENDPATH**/ ?>