<?php

namespace App\Http\Controllers;

use App\Models\BankAccount;
use App\Models\Payment;
use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CashbookController extends Controller
{
    /**
     * Display the cashbook index page
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Payment::class);

        $search = $request->get('search', '');
        $bankAccountId = $request->get('bank_account_id', '');
        $dateFrom = $request->get('date_from', '');
        $dateTo = $request->get('date_to', '');

        // Get all bank accounts for filter dropdown
        $bankAccounts = BankAccount::where('is_active', true)
            ->orderBy('name')
            ->get();

        // Build query for bank transactions
        $query = Payment::with(['currency', 'createdBy'])
            ->whereHasMorph('paymentable', [BankAccount::class]);

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('description', 'like', "%{$search}%")
                  ->orWhere('comment', 'like', "%{$search}%")
                  ->orWhere('reference_no', 'like', "%{$search}%");
            });
        }

        if ($bankAccountId) {
            $query->where('paymentable_id', $bankAccountId)
                  ->where('paymentable_type', BankAccount::class);
        }

        if ($dateFrom) {
            $query->whereDate('created_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->whereDate('created_at', '<=', $dateTo);
        }

        $transactions = $query->orderBy('created_at', 'desc')->paginate(20);

        // Calculate running balance for selected bank account
        $runningBalance = 0;
        $selectedBankAccount = null;
        
        if ($bankAccountId) {
            $selectedBankAccount = BankAccount::find($bankAccountId);
            if ($selectedBankAccount) {
                $runningBalance = $selectedBankAccount->opening_balance;
                
                // Add all transactions up to the current date
                $allTransactions = Payment::where('paymentable_id', $bankAccountId)
                    ->where('paymentable_type', BankAccount::class)
                    ->orderBy('created_at', 'asc')
                    ->get();
                
                foreach ($allTransactions as $transaction) {
                    $runningBalance += $transaction->amount;
                }
            }
        }

        return view('app.cashbook.index', compact(
            'transactions',
            'bankAccounts',
            'search',
            'bankAccountId',
            'dateFrom',
            'dateTo',
            'runningBalance',
            'selectedBankAccount'
        ));
    }

    /**
     * Show the form for creating a new bank transaction
     */
    public function create(Request $request)
    {
        $this->authorize('create', Payment::class);

        $bankAccounts = BankAccount::where('is_active', true)
            ->orderBy('name')
            ->get();

        $currencies = Currency::orderBy('name')->get();

        $selectedBankAccount = null;
        if ($request->bank_account_id) {
            $selectedBankAccount = BankAccount::find($request->bank_account_id);
        }

        return view('app.cashbook.create', compact(
            'bankAccounts',
            'currencies',
            'selectedBankAccount'
        ));
    }

    /**
     * Store a new bank transaction
     */
    public function store(Request $request)
    {
        $this->authorize('create', Payment::class);

        $validated = $request->validate([
            'bank_account_id' => 'required|exists:bank_accounts,id',
            'transaction_type' => 'required|in:credit,debit',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'required|string|max:255',
            'comment' => 'nullable|string|max:500',
            'reference_no' => 'nullable|string|max:100',
            'currency_id' => 'nullable|exists:currencies,id',
            'transaction_date' => 'required|date',
        ]);

        DB::beginTransaction();
        try {
            $bankAccount = BankAccount::findOrFail($validated['bank_account_id']);

            // Convert amount based on transaction type
            // Credit = positive (money coming in)
            // Debit = negative (money going out)
            $amount = $validated['transaction_type'] === 'credit' 
                ? abs($validated['amount']) 
                : -abs($validated['amount']);

            // Create payment record
            $payment = Payment::create([
                'amount' => $amount,
                'description' => $validated['description'],
                'comment' => $validated['comment'] ?? null,
                'reference_no' => $validated['reference_no'] ?? null,
                'currency_id' => $validated['currency_id'] ?? null,
                'paymentable_id' => $bankAccount->id,
                'paymentable_type' => BankAccount::class,
                'created_by' => auth()->id(),
                'created_at' => $validated['transaction_date'],
            ]);

            // Update bank account balance
            $bankAccount->current_balance += $amount;
            $bankAccount->save();

            DB::commit();

            return redirect()
                ->route('cashbook.index', ['bank_account_id' => $bankAccount->id])
                ->with('success', 'Bank transaction recorded successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()
                ->withInput()
                ->withErrors(['error' => 'Failed to record transaction: ' . $e->getMessage()]);
        }
    }

    /**
     * Show the form for editing a bank transaction
     */
    public function edit(Payment $payment)
    {
        $this->authorize('update', $payment);

        // Ensure this is a bank transaction
        if ($payment->paymentable_type !== BankAccount::class) {
            abort(404, 'Transaction not found.');
        }

        $bankAccounts = BankAccount::where('is_active', true)
            ->orderBy('name')
            ->get();

        $currencies = Currency::orderBy('name')->get();

        return view('app.cashbook.edit', compact(
            'payment',
            'bankAccounts',
            'currencies'
        ));
    }

    /**
     * Update a bank transaction
     */
    public function update(Request $request, Payment $payment)
    {
        $this->authorize('update', $payment);

        // Ensure this is a bank transaction
        if ($payment->paymentable_type !== BankAccount::class) {
            abort(404, 'Transaction not found.');
        }

        $validated = $request->validate([
            'bank_account_id' => 'required|exists:bank_accounts,id',
            'transaction_type' => 'required|in:credit,debit',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'required|string|max:255',
            'comment' => 'nullable|string|max:500',
            'reference_no' => 'nullable|string|max:100',
            'currency_id' => 'nullable|exists:currencies,id',
            'transaction_date' => 'required|date',
        ]);

        DB::beginTransaction();
        try {
            $oldBankAccount = $payment->paymentable;
            $newBankAccount = BankAccount::findOrFail($validated['bank_account_id']);

            // Reverse the old transaction from old bank account
            if ($oldBankAccount) {
                $oldBankAccount->current_balance -= $payment->amount;
                $oldBankAccount->save();
            }

            // Convert amount based on transaction type
            $amount = $validated['transaction_type'] === 'credit' 
                ? abs($validated['amount']) 
                : -abs($validated['amount']);

            // Update payment record
            $payment->update([
                'amount' => $amount,
                'description' => $validated['description'],
                'comment' => $validated['comment'] ?? null,
                'reference_no' => $validated['reference_no'] ?? null,
                'currency_id' => $validated['currency_id'] ?? null,
                'paymentable_id' => $newBankAccount->id,
                'paymentable_type' => BankAccount::class,
                'updated_by' => auth()->id(),
                'created_at' => $validated['transaction_date'],
            ]);

            // Apply new transaction to new bank account
            $newBankAccount->current_balance += $amount;
            $newBankAccount->save();

            DB::commit();

            return redirect()
                ->route('cashbook.index', ['bank_account_id' => $newBankAccount->id])
                ->with('success', 'Bank transaction updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()
                ->withInput()
                ->withErrors(['error' => 'Failed to update transaction: ' . $e->getMessage()]);
        }
    }

    /**
     * Delete a bank transaction
     */
    public function destroy(Payment $payment)
    {
        $this->authorize('delete', $payment);

        // Ensure this is a bank transaction
        if ($payment->paymentable_type !== BankAccount::class) {
            abort(404, 'Transaction not found.');
        }

        DB::beginTransaction();
        try {
            $bankAccount = $payment->paymentable;

            // Reverse the transaction from bank account balance
            if ($bankAccount) {
                $bankAccount->current_balance -= $payment->amount;
                $bankAccount->save();
            }

            $payment->delete();

            DB::commit();

            return redirect()
                ->route('cashbook.index')
                ->with('success', 'Bank transaction deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()
                ->withErrors(['error' => 'Failed to delete transaction: ' . $e->getMessage()]);
        }
    }

    /**
     * Get bank account balance via AJAX
     */
    public function getBankBalance(Request $request)
    {
        $bankAccount = BankAccount::find($request->bank_account_id);
        
        if (!$bankAccount) {
            return response()->json(['error' => 'Bank account not found'], 404);
        }

        return response()->json([
            'current_balance' => $bankAccount->current_balance,
            'currency' => $bankAccount->currency->code ?? 'USD',
        ]);
    }
}
