<?php

namespace App\Http\Controllers;

use App\Models\BankAccount;
use App\Models\Payment;
use App\Models\Currency;
use App\Models\JournalEntry;
use App\Models\JournalEntryLine;
use App\Models\Account;
use App\Models\FiscalYear;
use App\Models\FiscalPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CashbookController extends Controller
{
    /**
     * Display the cashbook index page
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Payment::class);

        $search = $request->get('search', '');
        $bankAccountId = $request->get('bank_account_id', '');
        $dateFrom = $request->get('date_from', '');
        $dateTo = $request->get('date_to', '');

        // Get all bank accounts for filter dropdown
        $bankAccounts = BankAccount::where('is_active', true)
            ->orderBy('name')
            ->get();

        // Build query for bank transactions
        $query = Payment::with(['currency', 'createdBy'])
            ->whereHasMorph('paymentable', [BankAccount::class]);

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('description', 'like', "%{$search}%")
                  ->orWhere('comment', 'like', "%{$search}%")
                  ->orWhere('reference_no', 'like', "%{$search}%");
            });
        }

        if ($bankAccountId) {
            $query->where('paymentable_id', $bankAccountId)
                  ->where('paymentable_type', BankAccount::class);
        }

        if ($dateFrom) {
            $query->whereDate('created_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->whereDate('created_at', '<=', $dateTo);
        }

        $transactions = $query->orderBy('created_at', 'desc')->paginate(20);

        // Calculate running balance for selected bank account
        $runningBalance = 0;
        $selectedBankAccount = null;
        
        if ($bankAccountId) {
            $selectedBankAccount = BankAccount::find($bankAccountId);
            if ($selectedBankAccount) {
                $runningBalance = $selectedBankAccount->opening_balance;
                
                // Add all transactions up to the current date
                $allTransactions = Payment::where('paymentable_id', $bankAccountId)
                    ->where('paymentable_type', BankAccount::class)
                    ->orderBy('created_at', 'asc')
                    ->get();
                
                foreach ($allTransactions as $transaction) {
                    $runningBalance += $transaction->amount;
                }
            }
        }

        return view('app.cashbook.index', compact(
            'transactions',
            'bankAccounts',
            'search',
            'bankAccountId',
            'dateFrom',
            'dateTo',
            'runningBalance',
            'selectedBankAccount'
        ));
    }

    /**
     * Show the form for creating a new bank transaction
     */
    public function create(Request $request)
    {
        $this->authorize('create', Payment::class);

        $bankAccounts = BankAccount::where('is_active', true)
            ->orderBy('name')
            ->get();

        $currencies = Currency::orderBy('name')->get();

        // Get GL accounts for contra account selection
        $glAccounts = Account::where('is_active', true)
            ->where('allows_manual_entries', true)
            ->orderBy('code')
            ->get()
            ->pluck('name_with_code', 'id');

        $selectedBankAccount = null;
        if ($request->bank_account_id) {
            $selectedBankAccount = BankAccount::find($request->bank_account_id);
        }

        return view('app.cashbook.create', compact(
            'bankAccounts',
            'currencies',
            'glAccounts',
            'selectedBankAccount'
        ));
    }

    /**
     * Store a new bank transaction
     */
    public function store(Request $request)
    {
        $this->authorize('create', Payment::class);

        $validated = $request->validate([
            'bank_account_id' => 'required|exists:bank_accounts,id',
            'transaction_type' => 'required|in:credit,debit',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'required|string|max:255',
            'comment' => 'nullable|string|max:500',
            'reference_no' => 'nullable|string|max:100',
            'currency_id' => 'nullable|exists:currencies,id',
            'transaction_date' => 'required|date',
            'contra_account_id' => 'nullable|exists:accounts,id',
        ]);

        DB::beginTransaction();
        try {
            $bankAccount = BankAccount::findOrFail($validated['bank_account_id']);

            // Convert amount based on transaction type
            // Credit = positive (money coming in)
            // Debit = negative (money going out)
            $amount = $validated['transaction_type'] === 'credit'
                ? abs($validated['amount'])
                : -abs($validated['amount']);

            // Create payment record
            $payment = Payment::create([
                'amount' => $amount,
                'description' => $validated['description'],
                'comment' => $validated['comment'] ?? null,
                'reference_no' => $validated['reference_no'] ?? null,
                'currency_id' => $validated['currency_id'] ?? null,
                'paymentable_id' => $bankAccount->id,
                'paymentable_type' => BankAccount::class,
                'created_by' => auth()->id(),
                'created_at' => $validated['transaction_date'],
            ]);

            // Update bank account balance
            $bankAccount->current_balance += $amount;
            $bankAccount->save();

            // Create Journal Entry for GL integration
            $this->createJournalEntry($payment, $bankAccount, $validated);

            DB::commit();

            return redirect()
                ->route('cashbook.index', ['bank_account_id' => $bankAccount->id])
                ->with('success', 'Bank transaction recorded successfully and posted to General Ledger.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()
                ->withInput()
                ->withErrors(['error' => 'Failed to record transaction: ' . $e->getMessage()]);
        }
    }

    /**
     * Show the form for editing a bank transaction
     */
    public function edit(Payment $payment)
    {
        $this->authorize('update', $payment);

        // Ensure this is a bank transaction
        if ($payment->paymentable_type !== BankAccount::class) {
            abort(404, 'Transaction not found.');
        }

        $bankAccounts = BankAccount::where('is_active', true)
            ->orderBy('name')
            ->get();

        $currencies = Currency::orderBy('name')->get();

        // Get GL accounts for contra account selection
        $glAccounts = Account::where('is_active', true)
            ->where('allows_manual_entries', true)
            ->orderBy('code')
            ->get()
            ->pluck('name_with_code', 'id');

        return view('app.cashbook.edit', compact(
            'payment',
            'bankAccounts',
            'currencies',
            'glAccounts'
        ));
    }

    /**
     * Update a bank transaction
     */
    public function update(Request $request, Payment $payment)
    {
        $this->authorize('update', $payment);

        // Ensure this is a bank transaction
        if ($payment->paymentable_type !== BankAccount::class) {
            abort(404, 'Transaction not found.');
        }

        $validated = $request->validate([
            'bank_account_id' => 'required|exists:bank_accounts,id',
            'transaction_type' => 'required|in:credit,debit',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'required|string|max:255',
            'comment' => 'nullable|string|max:500',
            'reference_no' => 'nullable|string|max:100',
            'currency_id' => 'nullable|exists:currencies,id',
            'transaction_date' => 'required|date',
            'contra_account_id' => 'nullable|exists:accounts,id',
        ]);

        DB::beginTransaction();
        try {
            $oldBankAccount = $payment->paymentable;
            $newBankAccount = BankAccount::findOrFail($validated['bank_account_id']);

            // Reverse the old transaction from old bank account
            if ($oldBankAccount) {
                $oldBankAccount->current_balance -= $payment->amount;
                $oldBankAccount->save();
            }

            // Reverse old journal entry
            $this->reverseJournalEntry($payment);

            // Convert amount based on transaction type
            $amount = $validated['transaction_type'] === 'credit'
                ? abs($validated['amount'])
                : -abs($validated['amount']);

            // Update payment record
            $payment->update([
                'amount' => $amount,
                'description' => $validated['description'],
                'comment' => $validated['comment'] ?? null,
                'reference_no' => $validated['reference_no'] ?? null,
                'currency_id' => $validated['currency_id'] ?? null,
                'paymentable_id' => $newBankAccount->id,
                'paymentable_type' => BankAccount::class,
                'updated_by' => auth()->id(),
                'created_at' => $validated['transaction_date'],
            ]);

            // Apply new transaction to new bank account
            $newBankAccount->current_balance += $amount;
            $newBankAccount->save();

            // Create new journal entry
            $this->createJournalEntry($payment, $newBankAccount, $validated);

            DB::commit();

            return redirect()
                ->route('cashbook.index', ['bank_account_id' => $newBankAccount->id])
                ->with('success', 'Bank transaction updated successfully and posted to General Ledger.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()
                ->withInput()
                ->withErrors(['error' => 'Failed to update transaction: ' . $e->getMessage()]);
        }
    }

    /**
     * Delete a bank transaction
     */
    public function destroy(Payment $payment)
    {
        $this->authorize('delete', $payment);

        // Ensure this is a bank transaction
        if ($payment->paymentable_type !== BankAccount::class) {
            abort(404, 'Transaction not found.');
        }

        DB::beginTransaction();
        try {
            $bankAccount = $payment->paymentable;

            // Reverse the transaction from bank account balance
            if ($bankAccount) {
                $bankAccount->current_balance -= $payment->amount;
                $bankAccount->save();
            }

            // Reverse journal entry
            $this->reverseJournalEntry($payment);

            $payment->delete();

            DB::commit();

            return redirect()
                ->route('cashbook.index')
                ->with('success', 'Bank transaction deleted successfully and reversed in General Ledger.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()
                ->withErrors(['error' => 'Failed to delete transaction: ' . $e->getMessage()]);
        }
    }

    /**
     * Get bank account balance via AJAX
     */
    public function getBankBalance(Request $request)
    {
        $bankAccount = BankAccount::find($request->bank_account_id);

        if (!$bankAccount) {
            return response()->json(['error' => 'Bank account not found'], 404);
        }

        return response()->json([
            'current_balance' => $bankAccount->current_balance,
            'currency' => $bankAccount->currency->code ?? 'USD',
        ]);
    }

    /**
     * Create journal entry for bank transaction (GL integration)
     */
    private function createJournalEntry(Payment $payment, BankAccount $bankAccount, array $validated)
    {
        // Get active fiscal year and period
        $fiscalYear = FiscalYear::where('is_active', true)->first();
        $fiscalPeriod = null;

        if ($fiscalYear) {
            $fiscalPeriod = FiscalPeriod::where('fiscal_year_id', $fiscalYear->id)
                ->where('is_active', true)
                ->where('start_date', '<=', $validated['transaction_date'])
                ->where('end_date', '>=', $validated['transaction_date'])
                ->first();
        }

        // Generate entry number
        $latestEntry = JournalEntry::latest()->first();
        $entryNumber = 'CB-' . date('Y') . '-' . sprintf('%06d', $latestEntry ? ($latestEntry->id + 1) : 1);

        // Create journal entry
        $journalEntry = JournalEntry::create([
            'entry_number' => $entryNumber,
            'entry_date' => $validated['transaction_date'],
            'reference_number' => $validated['reference_no'] ?? 'CASHBOOK-' . $payment->id,
            'description' => 'Cashbook: ' . $validated['description'],
            'entry_type' => 'system',
            'status' => 'posted',
            'fiscal_year_id' => $fiscalYear?->id,
            'fiscal_period_id' => $fiscalPeriod?->id,
            'currency_id' => $validated['currency_id'],
            'exchange_rate' => 1,
            'created_by' => auth()->id(),
            'posted_by' => auth()->id(),
            'posted_at' => now(),
        ]);

        // Determine contra account
        $contraAccount = null;
        if ($validated['contra_account_id']) {
            $contraAccount = Account::find($validated['contra_account_id']);
        } else {
            // Default contra accounts based on transaction type
            $contraAccount = $this->getDefaultContraAccount($validated['transaction_type']);
        }

        $absoluteAmount = abs($payment->amount);

        if ($validated['transaction_type'] === 'credit') {
            // Money coming in: Debit Bank Account, Credit Contra Account
            // Bank Account (Asset) increases with debit
            $journalEntry->journalEntryLines()->create([
                'account_id' => $bankAccount->gl_account_id,
                'description' => $validated['description'],
                'debit' => $absoluteAmount,
                'credit' => 0,
                'created_by' => auth()->id(),
            ]);

            // Contra account (usually Revenue/Income) increases with credit
            if ($contraAccount) {
                $journalEntry->journalEntryLines()->create([
                    'account_id' => $contraAccount->id,
                    'description' => $validated['description'],
                    'debit' => 0,
                    'credit' => $absoluteAmount,
                    'created_by' => auth()->id(),
                ]);
            }
        } else {
            // Money going out: Credit Bank Account, Debit Contra Account
            // Bank Account (Asset) decreases with credit
            $journalEntry->journalEntryLines()->create([
                'account_id' => $bankAccount->gl_account_id,
                'description' => $validated['description'],
                'debit' => 0,
                'credit' => $absoluteAmount,
                'created_by' => auth()->id(),
            ]);

            // Contra account (usually Expense) increases with debit
            if ($contraAccount) {
                $journalEntry->journalEntryLines()->create([
                    'account_id' => $contraAccount->id,
                    'description' => $validated['description'],
                    'debit' => $absoluteAmount,
                    'credit' => 0,
                    'created_by' => auth()->id(),
                ]);
            }
        }

        return $journalEntry;
    }

    /**
     * Get default contra account based on transaction type
     */
    private function getDefaultContraAccount(string $transactionType)
    {
        if ($transactionType === 'credit') {
            // For credits (money in), default to a revenue/income account
            return Account::where('code', 'LIKE', '4%') // Revenue accounts typically start with 4
                ->where('is_active', true)
                ->where('allows_manual_entries', true)
                ->first();
        } else {
            // For debits (money out), default to an expense account
            return Account::where('code', 'LIKE', '5%') // Expense accounts typically start with 5
                ->where('is_active', true)
                ->where('allows_manual_entries', true)
                ->first();
        }
    }

    /**
     * Reverse journal entry for a payment (when updating or deleting)
     */
    private function reverseJournalEntry(Payment $payment)
    {
        // Find journal entries related to this payment
        $journalEntries = JournalEntry::where('reference_number', 'LIKE', '%CASHBOOK-' . $payment->id . '%')
            ->where('status', 'posted')
            ->get();

        foreach ($journalEntries as $journalEntry) {
            // Create a reversing entry
            $reversingEntry = JournalEntry::create([
                'entry_number' => 'REV-' . $journalEntry->entry_number,
                'entry_date' => now()->toDateString(),
                'reference_number' => 'REVERSAL-' . $journalEntry->reference_number,
                'description' => 'Reversal: ' . $journalEntry->description,
                'entry_type' => 'system',
                'status' => 'posted',
                'fiscal_year_id' => $journalEntry->fiscal_year_id,
                'fiscal_period_id' => $journalEntry->fiscal_period_id,
                'currency_id' => $journalEntry->currency_id,
                'exchange_rate' => $journalEntry->exchange_rate,
                'created_by' => auth()->id(),
                'posted_by' => auth()->id(),
                'posted_at' => now(),
            ]);

            // Reverse all journal entry lines (swap debits and credits)
            foreach ($journalEntry->journalEntryLines as $line) {
                $reversingEntry->journalEntryLines()->create([
                    'account_id' => $line->account_id,
                    'description' => 'Reversal: ' . $line->description,
                    'debit' => $line->credit, // Swap credit to debit
                    'credit' => $line->debit, // Swap debit to credit
                    'created_by' => auth()->id(),
                ]);
            }

            // Mark original entry as reversed
            $journalEntry->update([
                'status' => 'reversed',
                'updated_by' => auth()->id(),
            ]);
        }
    }
}
