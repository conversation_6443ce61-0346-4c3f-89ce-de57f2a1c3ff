@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-journal-bookmark me-2"></i>Chart of Accounts
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group" role="group">
                @can('create', App\Models\Account::class)
                <a href="{{ route('accounts.create') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>Add Account
                </a>
                @endcan
                <a href="{{ route('account-types.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-tags me-1"></i>Account Types
                </a>
                <a href="{{ route('account-categories.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-collection me-1"></i>Categories
                </a>
                <button type="button" class="btn btn-outline-info btn-sm" onclick="window.print()">
                    <i class="bi bi-printer me-1"></i>Print
                </button>
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->
    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-funnel me-2"></i>Filters & Search
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('accounts.index') }}">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Search</label>
                        <input type="text" name="search" class="form-control"
                               placeholder="Search accounts..." value="{{ $search }}">
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">Account Type</label>
                        <select name="account_type_id" class="form-select">
                            <option value="">All Types</option>
                            @foreach($accountTypes as $id => $name)
                                <option value="{{ $id }}" {{ $accountTypeId == $id ? 'selected' : '' }}>
                                    {{ $name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">Category</label>
                        <select name="account_category_id" class="form-select">
                            <option value="">All Categories</option>
                            @foreach($accountCategories as $id => $name)
                                <option value="{{ $id }}" {{ $accountCategoryId == $id ? 'selected' : '' }}>
                                    {{ $name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>Filter
                        </button>
                        <a href="{{ route('accounts.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Accounts Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>Accounts
            </h5>
            <span class="badge bg-secondary">{{ $accounts->total() }} accounts</span>
        </div>

        <div class="table-responsive">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>Account Code & Name</th>
                        <th>Type & Category</th>
                        <th class="text-end">Debit Balance</th>
                        <th class="text-end">Credit Balance</th>
                        <th class="text-end">Net Balance</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($accounts as $account)
                    <tr>
                        <td>
                            <div>
                                <strong>
                                    <a href="{{ route('accounts.show', $account) }}" class="text-decoration-none">
                                        {{ $account->code }}
                                    </a>
                                </strong>
                                <br>
                                <span class="text-muted">{{ $account->name }}</span>
                                @if($account->parentAccount)
                                    <br><small class="text-info">
                                        <i class="bi bi-arrow-return-right me-1"></i>
                                        Sub-account of: {{ $account->parentAccount->code }}
                                    </small>
                                @endif
                            </div>
                        </td>
                        <td>
                            <div>
                                <span class="badge bg-primary">{{ $account->accountType->name ?? 'N/A' }}</span>
                                @if($account->accountCategory)
                                    <br><small class="text-muted mt-1">{{ $account->accountCategory->name }}</small>
                                @endif
                                <br><small class="text-muted">
                                    Normal: {{ ucfirst($account->normal_balance) }}
                                </small>
                            </div>
                        </td>
                        <td class="text-end">
                            <span class="fw-bold {{ $account->debit > 0 ? 'text-success' : 'text-muted' }}">
                                ${{ number_format($account->debit, 2) }}
                            </span>
                        </td>
                        <td class="text-end">
                            <span class="fw-bold {{ $account->credit > 0 ? 'text-danger' : 'text-muted' }}">
                                ${{ number_format($account->credit, 2) }}
                            </span>
                        </td>
                        <td class="text-end">
                            @php
                                $balance = $account->balance;
                                $isPositive = $balance >= 0;
                            @endphp
                            <span class="fw-bold {{ $isPositive ? 'text-success' : 'text-danger' }}">
                                ${{ number_format(abs($balance), 2) }}
                                @if(!$isPositive) <small>(CR)</small> @endif
                            </span>
                        </td>
                        <td class="text-center">
                            <div class="d-flex flex-column align-items-center">
                                @if($account->is_active)
                                    <span class="badge bg-success mb-1">Active</span>
                                @else
                                    <span class="badge bg-secondary mb-1">Inactive</span>
                                @endif

                                @if($account->is_system)
                                    <span class="badge bg-warning">System</span>
                                @endif

                                @if(!$account->allows_manual_entries)
                                    <span class="badge bg-info">Auto Only</span>
                                @endif
                            </div>
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                @can('view', $account)
                                <a href="{{ route('accounts.show', $account) }}"
                                   class="btn btn-sm btn-outline-info" title="View Details">
                                    <i class="bi bi-eye"></i>
                                </a>
                                @endcan

                                @can('update', $account)
                                <a href="{{ route('accounts.edit', $account) }}"
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                @endcan

                                @can('delete', $account)
                                @if(!$account->is_system)
                                <form method="POST" action="{{ route('accounts.destroy', $account) }}"
                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this account?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                @endif
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-journal-bookmark display-4 d-block mb-2"></i>
                                <p class="mb-0">No accounts found.</p>
                                @can('create', App\Models\Account::class)
                                <a href="{{ route('accounts.create') }}" class="btn btn-primary btn-sm mt-2">
                                    <i class="bi bi-plus-circle me-1"></i>Create First Account
                                </a>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($accounts->hasPages())
        <div class="card-footer">
            {{ $accounts->appends(request()->query())->links() }}
        </div>
        @endif
    </div>

    <!-- Summary Cards -->
    @if($accounts->count() > 0)
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $accounts->where('is_active', true)->count() }}</h3>
                    <small>Active Accounts</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">${{ number_format($accounts->sum('debit'), 2) }}</h3>
                    <small>Total Debits</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">${{ number_format($accounts->sum('credit'), 2) }}</h3>
                    <small>Total Credits</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $accounts->where('is_system', true)->count() }}</h3>
                    <small>System Accounts</small>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

@if(session('success'))
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            {{ session('success') }}
        </div>
    </div>
</div>
@endif

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
@endpush
