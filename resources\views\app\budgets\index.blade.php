@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-calculator me-2"></i>Budgets
        </x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Budget::class)
            <div class="btn-group" role="group">
                <a href="{{ route('budgets.create') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>Create Budget
                </a>
                <a href="{{ route('budgets.report') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-graph-up me-1"></i>Budget Report
                </a>
            </div>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-funnel me-2"></i>Filters
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('budgets.index') }}">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Search</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="Search budget name..." value="{{ $search }}">
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">Fiscal Year</label>
                        <select name="fiscal_year_id" class="form-select">
                            <option value="">All Fiscal Years</option>
                            @foreach($fiscalYears as $id => $name)
                                <option value="{{ $id }}" {{ $fiscalYearId == $id ? 'selected' : '' }}>
                                    {{ $name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Statuses</option>
                            <option value="draft" {{ $status === 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="approved" {{ $status === 'approved' ? 'selected' : '' }}>Approved</option>
                            <option value="active" {{ $status === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="closed" {{ $status === 'closed' ? 'selected' : '' }}>Closed</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>Filter
                        </button>
                        <a href="{{ route('budgets.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Budgets Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>Budget List
            </h5>
            <span class="badge bg-secondary">{{ $budgets->total() }} budgets</span>
        </div>
        
        <div class="table-responsive">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>Budget Name</th>
                        <th>Fiscal Year</th>
                        <th>Version</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Active</th>
                        <th>Created</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($budgets as $budget)
                    <tr>
                        <td>
                            <div>
                                <strong>{{ $budget->name }}</strong>
                                @if($budget->description)
                                    <br><small class="text-muted">{{ Str::limit($budget->description, 50) }}</small>
                                @endif
                            </div>
                        </td>
                        <td>
                            {{ $budget->fiscalYear->name ?? 'N/A' }}
                        </td>
                        <td>
                            {{ $budget->version ?? 'v1.0' }}
                        </td>
                        <td class="text-center">
                            @switch($budget->status)
                                @case('draft')
                                    <span class="badge bg-secondary">Draft</span>
                                    @break
                                @case('approved')
                                    <span class="badge bg-success">Approved</span>
                                    @break
                                @case('active')
                                    <span class="badge bg-primary">Active</span>
                                    @break
                                @case('closed')
                                    <span class="badge bg-dark">Closed</span>
                                    @break
                                @default
                                    <span class="badge bg-light text-dark">{{ ucfirst($budget->status) }}</span>
                            @endswitch
                        </td>
                        <td class="text-center">
                            @if($budget->is_active)
                                <i class="bi bi-check-circle-fill text-success" title="Active Budget"></i>
                            @else
                                <i class="bi bi-circle text-muted" title="Inactive"></i>
                            @endif
                        </td>
                        <td>
                            <span class="d-block">{{ $budget->created_at->format('M d, Y') }}</span>
                            <small class="text-muted">{{ $budget->created_at->format('H:i') }}</small>
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                @can('view', $budget)
                                <a href="{{ route('budgets.show', $budget) }}" 
                                   class="btn btn-sm btn-outline-info" title="View Details">
                                    <i class="bi bi-eye"></i>
                                </a>
                                @endcan
                                
                                @can('update', $budget)
                                <a href="{{ route('budgets.edit', $budget) }}" 
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                @endcan
                                
                                @can('delete', $budget)
                                <form method="POST" action="{{ route('budgets.destroy', $budget) }}" 
                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this budget?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-calculator display-4 d-block mb-2"></i>
                                <p class="mb-0">No budgets found.</p>
                                @can('create', App\Models\Budget::class)
                                <a href="{{ route('budgets.create') }}" class="btn btn-primary btn-sm mt-2">
                                    <i class="bi bi-plus-circle me-1"></i>Create First Budget
                                </a>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($budgets->hasPages())
        <div class="card-footer">
            {{ $budgets->appends(request()->query())->links() }}
        </div>
        @endif
    </div>

    <!-- Summary Cards -->
    @if($budgets->count() > 0)
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $budgets->where('status', 'active')->count() }}</h3>
                    <small>Active Budgets</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $budgets->where('status', 'approved')->count() }}</h3>
                    <small>Approved Budgets</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $budgets->where('status', 'draft')->count() }}</h3>
                    <small>Draft Budgets</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-dark text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $budgets->where('status', 'closed')->count() }}</h3>
                    <small>Closed Budgets</small>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

@if(session('success'))
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            {{ session('success') }}
        </div>
    </div>
</div>
@endif

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
@endpush
