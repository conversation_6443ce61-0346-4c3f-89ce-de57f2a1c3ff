@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-bank me-2"></i>Cashbook Management
        </x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Payment::class)
            <div class="btn-group" role="group">
                <a href="{{ route('cashbook.create') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>Add Bank Transaction
                </a>
                @if(Route::has('bank-accounts.index'))
                <a href="{{ route('bank-accounts.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-bank2 me-1"></i>Manage Banks
                </a>
                @endif
            </div>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End <PERSON> Header -->

    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-funnel me-2"></i>Filters
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('cashbook.index') }}">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Bank Account</label>
                        <select name="bank_account_id" class="form-select">
                            <option value="">All Bank Accounts</option>
                            @foreach($bankAccounts as $account)
                                <option value="{{ $account->id }}" 
                                    {{ $bankAccountId == $account->id ? 'selected' : '' }}>
                                    {{ $account->name }} ({{ $account->account_number }})
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Date From</label>
                        <input type="date" name="date_from" class="form-control" value="{{ $dateFrom }}">
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Date To</label>
                        <input type="date" name="date_to" class="form-control" value="{{ $dateTo }}">
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">Search</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="Search description, reference..." value="{{ $search }}">
                    </div>
                    
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>Filter
                        </button>
                        <a href="{{ route('cashbook.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bank Account Summary -->
    @if($selectedBankAccount)
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title mb-1">{{ $selectedBankAccount->name }}</h5>
                            <p class="card-text mb-0">{{ $selectedBankAccount->account_number }}</p>
                            <small class="opacity-75">{{ $selectedBankAccount->bank_name }}</small>
                        </div>
                        <div class="text-end">
                            <h3 class="mb-0">
                                {{ $selectedBankAccount->currency->symbol ?? '$' }}{{ number_format($runningBalance, 2) }}
                            </h3>
                            <small class="opacity-75">Current Balance</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-success mb-1">
                                {{ $selectedBankAccount->currency->symbol ?? '$' }}{{ number_format($transactions->where('amount', '>', 0)->sum('amount'), 2) }}
                            </h4>
                            <small class="text-muted">Total Credits</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-danger mb-1">
                                {{ $selectedBankAccount->currency->symbol ?? '$' }}{{ number_format(abs($transactions->where('amount', '<', 0)->sum('amount')), 2) }}
                            </h4>
                            <small class="text-muted">Total Debits</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Transactions Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>Bank Transactions
            </h5>
            <span class="badge bg-secondary">{{ $transactions->total() }} transactions</span>
        </div>
        
        <div class="table-responsive">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>Date</th>
                        <th>Bank Account</th>
                        <th>Description</th>
                        <th>Reference</th>
                        <th class="text-end">Debit</th>
                        <th class="text-end">Credit</th>
                        <th class="text-end">Balance</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($transactions as $transaction)
                    <tr>
                        <td>
                            <span class="d-block">{{ $transaction->created_at->format('M d, Y') }}</span>
                            <small class="text-muted">{{ $transaction->created_at->format('H:i') }}</small>
                        </td>
                        <td>
                            <div>
                                <strong>{{ $transaction->paymentable->name ?? 'N/A' }}</strong>
                                <br>
                                <small class="text-muted">{{ $transaction->paymentable->account_number ?? '' }}</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>{{ $transaction->description }}</strong>
                                @if($transaction->comment)
                                    <br><small class="text-muted">{{ $transaction->comment }}</small>
                                @endif
                            </div>
                        </td>
                        <td>
                            {{ $transaction->reference_no ?? '-' }}
                        </td>
                        <td class="text-end">
                            @if($transaction->amount < 0)
                                <span class="text-danger fw-bold">
                                    {{ $transaction->currency->symbol ?? '$' }}{{ number_format(abs($transaction->amount), 2) }}
                                </span>
                            @else
                                <span class="text-muted">-</span>
                            @endif
                        </td>
                        <td class="text-end">
                            @if($transaction->amount > 0)
                                <span class="text-success fw-bold">
                                    {{ $transaction->currency->symbol ?? '$' }}{{ number_format($transaction->amount, 2) }}
                                </span>
                            @else
                                <span class="text-muted">-</span>
                            @endif
                        </td>
                        <td class="text-end">
                            <span class="fw-bold">
                                {{ $transaction->currency->symbol ?? '$' }}{{ number_format($transaction->paymentable->current_balance ?? 0, 2) }}
                            </span>
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                @can('update', $transaction)
                                <a href="{{ route('cashbook.edit', $transaction) }}" 
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                @endcan
                                
                                @can('delete', $transaction)
                                <form method="POST" action="{{ route('cashbook.destroy', $transaction) }}" 
                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this transaction?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-inbox display-4 d-block mb-2"></i>
                                <p class="mb-0">No bank transactions found.</p>
                                @can('create', App\Models\Payment::class)
                                <a href="{{ route('cashbook.create') }}" class="btn btn-primary btn-sm mt-2">
                                    <i class="bi bi-plus-circle me-1"></i>Add First Transaction
                                </a>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($transactions->hasPages())
        <div class="card-footer">
            {{ $transactions->appends(request()->query())->links() }}
        </div>
        @endif
    </div>
</div>

@if(session('success'))
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            {{ session('success') }}
        </div>
    </div>
</div>
@endif

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
@endpush
