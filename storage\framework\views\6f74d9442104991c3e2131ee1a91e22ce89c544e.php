

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707 = $component; } ?>
<?php $component = App\View\Components\PageHeader::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\PageHeader::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('title', null, []); ?> Currencies <?php $__env->endSlot(); ?>
         <?php $__env->slot('controls', null, []); ?> 
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Currency::class)): ?>
            <a href="<?php echo e(route('currencies.create')); ?>" class="btn btn-info btn-sm">
                <?php echo app('translator')->get('crud.common.create'); ?> Currency
            </a>
            <?php endif; ?>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707)): ?>
<?php $component = $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707; ?>
<?php unset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707); ?>
<?php endif; ?>
    <!-- End Page Header -->

    <div class="searchbar mt-4 mb-5">
        <form class="d-flex" id="filter">
            <div class="me-3">
                <input
                    type="text"
                    name="search"
                    value="<?php echo e($search ?? ''); ?>"
                    class="form-control"
                    placeholder="<?php echo e(__('crud.common.search')); ?>"
                    autocomplete="off"
                />
            </div>
            <div class="me-3">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-search"></i>
                </button>
            </div>
        </form>
    </div>

    <div class="card card-table">
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            <?php echo app('translator')->get('crud.currencies.inputs.name'); ?>
                        </th>
                        <th class="text-left">
                            <?php echo app('translator')->get('crud.currencies.inputs.code'); ?>
                        </th>
                        <th class="text-left">
                            <?php echo app('translator')->get('crud.currencies.inputs.symbol'); ?>
                        </th>
                        <th class="text-left">
                            <?php echo app('translator')->get('crud.currencies.inputs.decimal_places'); ?>
                        </th>
                        <th class="text-left">
                            <?php echo app('translator')->get('crud.currencies.inputs.exchange_rate'); ?>
                        </th>
                        <th class="text-center">
                            <?php echo app('translator')->get('crud.currencies.inputs.is_base_currency'); ?>
                        </th>
                        <th class="text-center">
                            <?php echo app('translator')->get('crud.currencies.inputs.is_default'); ?>
                        </th>
                        <th class="text-center">
                            <?php echo app('translator')->get('crud.currencies.inputs.is_active'); ?>
                        </th>
                        <th class="text-center">
                            <?php echo app('translator')->get('crud.common.actions'); ?>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td><?php echo e($currency->name ?? '-'); ?></td>
                        <td><?php echo e($currency->code ?? '-'); ?></td>
                        <td><?php echo e($currency->symbol ?? '-'); ?></td>
                        <td><?php echo e($currency->decimal_places ?? '-'); ?></td>
                        <td><?php echo e($currency->exchange_rate ?? '-'); ?></td>
                        <td class="text-center">
                            <span class="badge <?php echo e($currency->is_base_currency ? 'bg-success' : 'bg-secondary'); ?>">
                                <?php echo e($currency->is_base_currency ? 'Yes' : 'No'); ?>

                            </span>
                        </td>
                        <td class="text-center">
                            <span class="badge <?php echo e($currency->is_default ? 'bg-success' : 'bg-secondary'); ?>">
                                <?php echo e($currency->is_default ? 'Yes' : 'No'); ?>

                            </span>
                        </td>
                        <td class="text-center">
                            <span class="badge <?php echo e($currency->is_active ? 'bg-success' : 'bg-secondary'); ?>">
                                <?php echo e($currency->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                        </td>
                        <td class="text-center" style="width: 134px;">
                            <div
                                role="group"
                                aria-label="Row Actions"
                                class="btn-group"
                            >
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $currency)): ?>
                                <a
                                    href="<?php echo e(route('currencies.edit', $currency)); ?>"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        edit
                                    </button>
                                </a>
                                <?php endif; ?> <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $currency)): ?>
                                <a
                                    href="<?php echo e(route('currencies.show', $currency)); ?>"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        view
                                    </button>
                                </a>
                                <?php endif; ?> <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $currency)): ?>
                                <form
                                    action="<?php echo e(route('currencies.destroy', $currency)); ?>"
                                    method="POST"
                                    onsubmit="return confirm('<?php echo e(__('crud.common.are_you_sure')); ?>')"
                                >
                                    <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                    <button
                                        type="submit"
                                        class="btn btn-light text-danger m-1"
                                    >
                                        del
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="9" class="text-center">
                            <?php echo app('translator')->get('crud.common.no_items_found'); ?>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        <div class="card-footer d-flex justify-content-center">
            <?php echo $currencies->render(); ?>

        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
  $("document").ready(function () {
    dataTableBtn()
  });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Acounting\github\accounting\resources\views/app/currencies/index.blade.php ENDPATH**/ ?>