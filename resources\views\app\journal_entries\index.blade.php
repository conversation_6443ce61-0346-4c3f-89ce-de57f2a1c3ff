@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-journal-text me-2"></i>Journal Entries
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group" role="group">
                @can('create', App\Models\JournalEntry::class)
                <a href="{{ route('journal-entries.create') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>Add Entry
                </a>
                @endcan
                <button type="button" class="btn btn-outline-info btn-sm" onclick="window.print()">
                    <i class="bi bi-printer me-1"></i>Print
                </button>
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-funnel me-2"></i>Filters & Search
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('journal-entries.index') }}">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Search</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="Search entries..." value="{{ $search }}">
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="draft" {{ $status == 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="approved" {{ $status == 'approved' ? 'selected' : '' }}>Approved</option>
                            <option value="posted" {{ $status == 'posted' ? 'selected' : '' }}>Posted</option>
                            <option value="reversed" {{ $status == 'reversed' ? 'selected' : '' }}>Reversed</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">From Date</label>
                        <input type="date" name="from_date" class="form-control" value="{{ $fromDate }}">
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">To Date</label>
                        <input type="date" name="to_date" class="form-control" value="{{ $toDate }}">
                    </div>
                    
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>Filter
                        </button>
                        <a href="{{ route('journal-entries.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Journal Entries Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>Journal Entries
            </h5>
            <span class="badge bg-secondary">{{ $journalEntries->total() }} entries</span>
        </div>
        
        <div class="table-responsive">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>Entry Number</th>
                        <th>Date</th>
                        <th>Description</th>
                        <th>Reference</th>
                        <th class="text-end">Total Debit</th>
                        <th class="text-end">Total Credit</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($journalEntries as $entry)
                    <tr>
                        <td>
                            <div>
                                <strong>
                                    <a href="{{ route('journal-entries.show', $entry) }}" class="text-decoration-none">
                                        {{ $entry->entry_number }}
                                    </a>
                                </strong>
                                <br>
                                <small class="text-muted">{{ $entry->entry_type }}</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                {{ $entry->entry_date->format('M d, Y') }}
                                <br>
                                <small class="text-muted">{{ $entry->created_at->format('H:i') }}</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                {{ $entry->description ?: 'No description' }}
                                @if($entry->fiscalYear)
                                    <br><small class="text-info">FY: {{ $entry->fiscalYear->name }}</small>
                                @endif
                            </div>
                        </td>
                        <td>{{ $entry->reference_number ?: '-' }}</td>
                        <td class="text-end">
                            <span class="fw-bold text-success">
                                ${{ number_format($entry->journalEntryLines->sum('debit'), 2) }}
                            </span>
                        </td>
                        <td class="text-end">
                            <span class="fw-bold text-danger">
                                ${{ number_format($entry->journalEntryLines->sum('credit'), 2) }}
                            </span>
                        </td>
                        <td class="text-center">
                            @switch($entry->status)
                                @case('draft')
                                    <span class="badge bg-secondary">Draft</span>
                                    @break
                                @case('approved')
                                    <span class="badge bg-warning">Approved</span>
                                    @break
                                @case('posted')
                                    <span class="badge bg-success">Posted</span>
                                    @break
                                @case('reversed')
                                    <span class="badge bg-danger">Reversed</span>
                                    @break
                                @default
                                    <span class="badge bg-light text-dark">{{ ucfirst($entry->status) }}</span>
                            @endswitch
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                @can('view', $entry)
                                <a href="{{ route('journal-entries.show', $entry) }}" 
                                   class="btn btn-sm btn-outline-info" title="View Details">
                                    <i class="bi bi-eye"></i>
                                </a>
                                @endcan
                                
                                @can('update', $entry)
                                @if($entry->status === 'draft')
                                <a href="{{ route('journal-entries.edit', $entry) }}" 
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                @endif
                                @endcan
                                
                                @can('delete', $entry)
                                @if($entry->status === 'draft')
                                <form method="POST" action="{{ route('journal-entries.destroy', $entry) }}" 
                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this journal entry?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                @endif
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-journal-text display-4 d-block mb-2"></i>
                                <p class="mb-0">No journal entries found.</p>
                                @can('create', App\Models\JournalEntry::class)
                                <a href="{{ route('journal-entries.create') }}" class="btn btn-primary btn-sm mt-2">
                                    <i class="bi bi-plus-circle me-1"></i>Create First Entry
                                </a>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($journalEntries->hasPages())
        <div class="card-footer">
            {{ $journalEntries->appends(request()->query())->links() }}
        </div>
        @endif
    </div>

    <!-- Summary Cards -->
    @if($journalEntries->count() > 0)
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $journalEntries->where('status', 'posted')->count() }}</h3>
                    <small>Posted Entries</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $journalEntries->where('status', 'draft')->count() }}</h3>
                    <small>Draft Entries</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">${{ number_format($journalEntries->flatMap->journalEntryLines->sum('debit'), 2) }}</h3>
                    <small>Total Debits</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">${{ number_format($journalEntries->flatMap->journalEntryLines->sum('credit'), 2) }}</h3>
                    <small>Total Credits</small>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

@if(session('success'))
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            {{ session('success') }}
        </div>
    </div>
</div>
@endif

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
@endpush
