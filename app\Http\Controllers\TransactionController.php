<?php

namespace App\Http\Controllers;

use App\Models\Status;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Facades\App\Libraries\InvoiceHandler;

class TransactionController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Payment::class);

        $search = $request->get('search', '');

        // Get all payments as transactions
        $transactions = Payment::search($search)->latest();

        if(request()->account_id) {
            $transactions->where("paymentable_id", request()->account_id)
                        ->where("paymentable_type", "App\\Models\\Account");
        }

        $transactions = $transactions->paginate()
            ->withQueryString();

        return view('app.transactions.index', compact('transactions'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Payment::class);

        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');

        return view('app.transactions.create', compact('statuses'));
    }

    /**
     * @param \App\Http\Requests\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', Payment::class);

        $validated = $request->all();

        // Set amount based on type (negative for debt, positive for credit)
        if ($request->type === 'debt') {
            $validated['amount'] = -abs($request->amount); // Negative for debt
        } else {
            $validated['amount'] = abs($request->amount); // Positive for credit
        }

        // Set polymorphic relationship if account is specified
        if ($request->account_id) {
            $validated['paymentable_type'] = 'App\\Models\\Account';
            $validated['paymentable_id'] = $request->account_id;
        }

        $payment = Payment::create($validated);

        // Redirect based on whether account is specified
        if ($payment->paymentable_type === 'App\\Models\\Account') {
            return redirect()
                ->route('accounts.show', $payment->paymentable_id)
                ->withSuccess(__('crud.common.saved'));
        }

        return redirect()
            ->route('transactions.index')
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Transaction $transaction
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Payment $payment)
    {
        $this->authorize('view', $payment);

        return view('app.transactions.show', compact('payment'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Transaction $transaction
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Payment $payment)
    {
        $this->authorize('update', $payment);

        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');

        return view('app.transactions.edit', compact('payment', 'statuses'));
    }

    /**
     * @param \App\Http\Requests\Request $request
     * @param \App\Models\Transaction $transaction
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Payment $payment)
    {
        $this->authorize('update', $payment);

        $validated = $request->all();

        // Set amount based on type (negative for debt, positive for credit)
        if ($request->type === 'debt') {
            $validated['amount'] = -abs($request->amount); // Negative for debt
        } else {
            $validated['amount'] = abs($request->amount); // Positive for credit
        }

        $payment->update($validated);

        // Redirect based on whether account is specified
        if ($payment->paymentable_type === 'App\\Models\\Account') {
            return redirect()
                ->route('accounts.show', $payment->paymentable_id)
                ->withSuccess(__('crud.common.saved'));
        }

        return redirect()
            ->route('transactions.index')
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Transaction $transaction
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Transaction $transaction)
    {
        $this->authorize('delete', $transaction);

        if ($transaction->name) {
            Storage::delete($transaction->name);
        }

        $transaction->delete();

        return redirect()
            ->back()
            // ->route('transactions.index')
            ->withSuccess(__('crud.common.removed'));
    }


}
