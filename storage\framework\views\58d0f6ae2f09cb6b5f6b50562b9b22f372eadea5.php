

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707 = $component; } ?>
<?php $component = App\View\Components\PageHeader::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\PageHeader::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('icon', null, []); ?> journal-text <?php $__env->endSlot(); ?>
         <?php $__env->slot('title', null, []); ?> Account Types <?php $__env->endSlot(); ?>
         <?php $__env->slot('subtitle', null, []); ?> Manage chart of accounts classification <?php $__env->endSlot(); ?>
         <?php $__env->slot('breadcrumbs', null, []); ?> 
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Accounting</a></li>
            <li class="breadcrumb-item active" aria-current="page">Account Types</li>
         <?php $__env->endSlot(); ?>
         <?php $__env->slot('controls', null, []); ?> 
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\AccountType::class)): ?>
            <a href="<?php echo e(route('account-types.create')); ?>" class="btn btn-primary">
                <i class="bi bi-plus-circle me-1"></i> Add Account Type
            </a>
            <?php endif; ?>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707)): ?>
<?php $component = $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707; ?>
<?php unset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707); ?>
<?php endif; ?>
    <!-- End Page Header -->

    <!-- Search & Filters -->
    <div class="card mb-3">
        <div class="card-body">
            <form action="<?php echo e(route('account-types.index')); ?>" method="GET" class="row g-3">
                <div class="col-md-6">
                    <label for="search" class="form-label">Search Account Types</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="search" name="search" placeholder="Name, code, classification..." value="<?php echo e($search ?? ''); ?>">
                    </div>
                </div>
                <div class="col-md-3">
                    <label for="classification" class="form-label">Classification</label>
                    <select class="form-select js-select" id="classification" name="classification">
                        <option value="">All Classifications</option>
                        <option value="asset" <?php echo e(request('classification') == 'asset' ? 'selected' : ''); ?>>Asset</option>
                        <option value="liability" <?php echo e(request('classification') == 'liability' ? 'selected' : ''); ?>>Liability</option>
                        <option value="equity" <?php echo e(request('classification') == 'equity' ? 'selected' : ''); ?>>Equity</option>
                        <option value="revenue" <?php echo e(request('classification') == 'revenue' ? 'selected' : ''); ?>>Revenue</option>
                        <option value="expense" <?php echo e(request('classification') == 'expense' ? 'selected' : ''); ?>>Expense</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-filter me-1"></i> Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Account Types Table -->
    <div class="card">
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">Account Types</h4>
                </div>
                <div class="col-auto">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-download me-1"></i> Export
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="exportDropdown">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-excel me-1"></i> Excel</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-pdf me-1"></i> PDF</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-text me-1"></i> CSV</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th><?php echo app('translator')->get('crud.account_types.inputs.name'); ?></th>
                        <th><?php echo app('translator')->get('crud.account_types.inputs.code'); ?></th>
                        <th><?php echo app('translator')->get('crud.account_types.inputs.classification'); ?></th>
                        <th class="text-center"><?php echo app('translator')->get('crud.account_types.inputs.is_active'); ?></th>
                        <th class="text-center"><?php echo app('translator')->get('crud.common.actions'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $accountTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $accountType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar avatar-xs avatar-circle">
                                        <span class="avatar-initials"><?php echo e(substr($accountType->name, 0, 1)); ?></span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5 class="mb-0"><?php echo e($accountType->name ?? '-'); ?></h5>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-soft-primary"><?php echo e($accountType->code ?? '-'); ?></span>
                        </td>
                        <td>
                            <?php
                                $classColors = [
                                    'asset' => 'success',
                                    'liability' => 'danger',
                                    'equity' => 'info',
                                    'revenue' => 'primary',
                                    'expense' => 'warning'
                                ];
                                $color = $classColors[$accountType->classification] ?? 'secondary';
                            ?>
                            <span class="badge bg-soft-<?php echo e($color); ?> text-<?php echo e($color); ?>">
                                <?php echo e(ucfirst($accountType->classification) ?? '-'); ?>

                            </span>
                        </td>
                        <td class="text-center">
                            <?php if($accountType->is_active): ?>
                                <span class="badge bg-soft-success text-success">
                                    <i class="bi bi-check-circle me-1"></i> Active
                                </span>
                            <?php else: ?>
                                <span class="badge bg-soft-danger text-danger">
                                    <i class="bi bi-x-circle me-1"></i> Inactive
                                </span>
                            <?php endif; ?>
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group" aria-label="Row Actions">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $accountType)): ?>
                                <a href="<?php echo e(route('account-types.edit', $accountType)); ?>" class="btn btn-outline-primary btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit">
                                    <i class="bi bi-pencil-square"></i>
                                </a>
                                <?php endif; ?> 
                                
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $accountType)): ?>
                                <a href="<?php echo e(route('account-types.show', $accountType)); ?>" class="btn btn-outline-info btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="View">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <?php endif; ?> 
                                
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $accountType)): ?>
                                <form action="<?php echo e(route('account-types.destroy', $accountType)); ?>" method="POST" onsubmit="return confirm('<?php echo e(__('crud.common.are_you_sure')); ?>')">
                                    <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-outline-danger btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="Delete" <?php echo e($accountType->is_system ? 'disabled' : ''); ?>>
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="5" class="text-center p-4">
                            <div class="d-flex flex-column align-items-center justify-content-center py-5">
                                <i class="bi bi-journal-text text-muted" style="font-size: 3rem;"></i>
                                <h5 class="mt-4">No account types found</h5>
                                <p class="text-muted">Try adjusting your search or filter to find what you're looking for.</p>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\AccountType::class)): ?>
                                <a href="<?php echo e(route('account-types.create')); ?>" class="btn btn-primary mt-2">
                                    <i class="bi bi-plus-circle me-1"></i> Add New Account Type
                                </a>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <?php if(isset($accountTypes) && $accountTypes->hasPages()): ?>
        <div class="card-footer">
            <div class="d-flex justify-content-center">
                <?php echo e($accountTypes->links()); ?>

            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    $(document).ready(function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Acounting\github\accounting\resources\views/app/account_types/index.blade.php ENDPATH**/ ?>