

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707 = $component; } ?>
<?php $component = App\View\Components\PageHeader::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\PageHeader::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('title', null, []); ?> 
            <i class="bi bi-journal-bookmark me-2"></i>Chart of Accounts
         <?php $__env->endSlot(); ?>
         <?php $__env->slot('controls', null, []); ?> 
            <div class="btn-group" role="group">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Account::class)): ?>
                <a href="<?php echo e(route('accounts.create')); ?>" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>Add Account
                </a>
                <?php endif; ?>
                <a href="<?php echo e(route('account-types.index')); ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-tags me-1"></i>Account Types
                </a>
                <a href="<?php echo e(route('account-categories.index')); ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-collection me-1"></i>Categories
                </a>
                <button type="button" class="btn btn-outline-info btn-sm" onclick="window.print()">
                    <i class="bi bi-printer me-1"></i>Print
                </button>
            </div>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707)): ?>
<?php $component = $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707; ?>
<?php unset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707); ?>
<?php endif; ?>
    <!-- End Page Header -->
    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-funnel me-2"></i>Filters & Search
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('accounts.index')); ?>">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Search</label>
                        <input type="text" name="search" class="form-control"
                               placeholder="Search accounts..." value="<?php echo e($search); ?>">
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">Account Type</label>
                        <select name="account_type_id" class="form-select">
                            <option value="">All Types</option>
                            <?php $__currentLoopData = $accountTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $id => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($id); ?>" <?php echo e($accountTypeId == $id ? 'selected' : ''); ?>>
                                    <?php echo e($name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">Category</label>
                        <select name="account_category_id" class="form-select">
                            <option value="">All Categories</option>
                            <?php $__currentLoopData = $accountCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $id => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($id); ?>" <?php echo e($accountCategoryId == $id ? 'selected' : ''); ?>>
                                    <?php echo e($name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>Filter
                        </button>
                        <a href="<?php echo e(route('accounts.index')); ?>" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Accounts Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>Accounts
            </h5>
            <span class="badge bg-secondary"><?php echo e($accounts->total()); ?> accounts</span>
        </div>

        <div class="table-responsive">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>Account Code & Name</th>
                        <th>Type & Category</th>
                        <th class="text-end">Debit Balance</th>
                        <th class="text-end">Credit Balance</th>
                        <th class="text-end">Net Balance</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td>
                            <div>
                                <strong>
                                    <a href="<?php echo e(route('accounts.show', $account)); ?>" class="text-decoration-none">
                                        <?php echo e($account->code); ?>

                                    </a>
                                </strong>
                                <br>
                                <span class="text-muted"><?php echo e($account->name); ?></span>
                                <?php if($account->parentAccount): ?>
                                    <br><small class="text-info">
                                        <i class="bi bi-arrow-return-right me-1"></i>
                                        Sub-account of: <?php echo e($account->parentAccount->code); ?>

                                    </small>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <div>
                                <span class="badge bg-primary"><?php echo e($account->accountType->name ?? 'N/A'); ?></span>
                                <?php if($account->accountCategory): ?>
                                    <br><small class="text-muted mt-1"><?php echo e($account->accountCategory->name); ?></small>
                                <?php endif; ?>
                                <br><small class="text-muted">
                                    Normal: <?php echo e(ucfirst($account->normal_balance)); ?>

                                </small>
                            </div>
                        </td>
                        <td class="text-end">
                            <span class="fw-bold <?php echo e($account->debit > 0 ? 'text-success' : 'text-muted'); ?>">
                                $<?php echo e(number_format($account->debit, 2)); ?>

                            </span>
                        </td>
                        <td class="text-end">
                            <span class="fw-bold <?php echo e($account->credit > 0 ? 'text-danger' : 'text-muted'); ?>">
                                $<?php echo e(number_format($account->credit, 2)); ?>

                            </span>
                        </td>
                        <td class="text-end">
                            <?php
                                $balance = $account->balance;
                                $isPositive = $balance >= 0;
                            ?>
                            <span class="fw-bold <?php echo e($isPositive ? 'text-success' : 'text-danger'); ?>">
                                $<?php echo e(number_format(abs($balance), 2)); ?>

                                <?php if(!$isPositive): ?> <small>(CR)</small> <?php endif; ?>
                            </span>
                        </td>
                        <td class="text-center">
                            <div class="d-flex flex-column align-items-center">
                                <?php if($account->is_active): ?>
                                    <span class="badge bg-success mb-1">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary mb-1">Inactive</span>
                                <?php endif; ?>

                                <?php if($account->is_system): ?>
                                    <span class="badge bg-warning">System</span>
                                <?php endif; ?>

                                <?php if(!$account->allows_manual_entries): ?>
                                    <span class="badge bg-info">Auto Only</span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $account)): ?>
                                <a href="<?php echo e(route('accounts.show', $account)); ?>"
                                   class="btn btn-sm btn-outline-info" title="View Details">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <?php endif; ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $account)): ?>
                                <a href="<?php echo e(route('accounts.edit', $account)); ?>"
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <?php endif; ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $account)): ?>
                                <?php if(!$account->is_system): ?>
                                <form method="POST" action="<?php echo e(route('accounts.destroy', $account)); ?>"
                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this account?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-journal-bookmark display-4 d-block mb-2"></i>
                                <p class="mb-0">No accounts found.</p>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Account::class)): ?>
                                <a href="<?php echo e(route('accounts.create')); ?>" class="btn btn-primary btn-sm mt-2">
                                    <i class="bi bi-plus-circle me-1"></i>Create First Account
                                </a>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if($accounts->hasPages()): ?>
        <div class="card-footer">
            <?php echo e($accounts->appends(request()->query())->links()); ?>

        </div>
        <?php endif; ?>
    </div>

    <!-- Summary Cards -->
    <?php if($accounts->count() > 0): ?>
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1"><?php echo e($accounts->where('is_active', true)->count()); ?></h3>
                    <small>Active Accounts</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">$<?php echo e(number_format($accounts->sum('debit'), 2)); ?></h3>
                    <small>Total Debits</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">$<?php echo e(number_format($accounts->sum('credit'), 2)); ?></h3>
                    <small>Total Credits</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1"><?php echo e($accounts->where('is_system', true)->count()); ?></h3>
                    <small>System Accounts</small>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php if(session('success')): ?>
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            <?php echo e(session('success')); ?>

        </div>
    </div>
</div>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Acounting\github\accounting\resources\views/app/accounts/index.blade.php ENDPATH**/ ?>