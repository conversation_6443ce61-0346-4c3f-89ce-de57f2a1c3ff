

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707 = $component; } ?>
<?php $component = App\View\Components\PageHeader::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\PageHeader::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('icon', null, []); ?> journal-text <?php $__env->endSlot(); ?>
         <?php $__env->slot('title', null, []); ?> <?php echo e($accountType->name); ?> <?php $__env->endSlot(); ?>
         <?php $__env->slot('subtitle', null, []); ?> Account Type Details <?php $__env->endSlot(); ?>
         <?php $__env->slot('breadcrumbs', null, []); ?> 
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Accounting</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('account-types.index')); ?>">Account Types</a></li>
            <li class="breadcrumb-item active" aria-current="page">View</li>
         <?php $__env->endSlot(); ?>
         <?php $__env->slot('controls', null, []); ?> 
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $accountType)): ?>
            <a href="<?php echo e(route('account-types.edit', $accountType)); ?>" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> Edit Account Type
            </a>
            <?php endif; ?>
            <a href="<?php echo e(route('account-types.index')); ?>" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Account Types
            </a>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707)): ?>
<?php $component = $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707; ?>
<?php unset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707); ?>
<?php endif; ?>
    <!-- End Page Header -->

    <div class="row">
        <!-- Account Type Overview Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-info-circle me-2"></i>Account Type Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <?php
                            $classColors = [
                                'asset' => 'success',
                                'liability' => 'danger',
                                'equity' => 'info',
                                'revenue' => 'primary',
                                'expense' => 'warning'
                            ];
                            $color = $classColors[$accountType->classification] ?? 'secondary';
                        ?>
                        <div class="avatar avatar-xl avatar-circle mb-3 mx-auto bg-soft-<?php echo e($color); ?>">
                            <span class="avatar-initials text-<?php echo e($color); ?>"><?php echo e(substr($accountType->name, 0, 1)); ?></span>
                        </div>
                        <h4 class="mb-1"><?php echo e($accountType->name); ?></h4>
                        <p class="text-muted">
                            <span class="badge bg-soft-<?php echo e($color); ?> text-<?php echo e($color); ?>">
                                <?php echo e(ucfirst($accountType->classification)); ?>

                            </span>
                        </p>
                    </div>

                    <hr>

                    <div class="mb-4">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Code:</span>
                            <span class="badge bg-soft-primary"><?php echo e($accountType->code ?? 'N/A'); ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Status:</span>
                            <span class="badge bg-<?php echo e($accountType->is_active ? 'success' : 'danger'); ?>">
                                <?php echo e($accountType->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Type:</span>
                            <span class="badge bg-<?php echo e($accountType->is_system ? 'info' : 'secondary'); ?>">
                                <?php echo e($accountType->is_system ? 'System' : 'User-defined'); ?>

                            </span>
                        </div>
                    </div>

                    <hr>

                    <h6 class="mb-3">Description</h6>
                    <p class="mb-0"><?php echo e($accountType->description ?? 'No description available.'); ?></p>
                </div>
            </div>
        </div>

        <!-- Account Type Statistics Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-graph-up me-2"></i>Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="card card-dashed h-100">
                                <div class="card-body d-flex justify-content-center flex-column text-center">
                                    <h2 class="mb-1"><?php echo e($accountType->categories()->count()); ?></h2>
                                    <span class="text-muted">Categories</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card card-dashed h-100">
                                <div class="card-body d-flex justify-content-center flex-column text-center">
                                    <h2 class="mb-1"><?php echo e($accountType->accounts()->count()); ?></h2>
                                    <span class="text-muted">Accounts</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <h6 class="mb-3">System Information</h6>
                    <div class="d-flex justify-content-between mb-1">
                        <span class="text-muted">Created:</span>
                        <span><?php echo e($accountType->created_at->format('M d, Y h:i A')); ?></span>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <span class="text-muted">Last Updated:</span>
                        <span><?php echo e($accountType->updated_at->format('M d, Y h:i A')); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Information Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-link-45deg me-2"></i>Related Information
                    </h5>
                </div>
                <div class="card-body">
                    <h6 class="mb-3">Financial Statement</h6>
                    <p class="mb-4">
                        <?php
                            $statements = [
                                'asset' => 'Balance Sheet (Assets)',
                                'liability' => 'Balance Sheet (Liabilities)',
                                'equity' => 'Balance Sheet (Equity)',
                                'revenue' => 'Income Statement (Revenue)',
                                'expense' => 'Income Statement (Expenses)'
                            ];
                        ?>
                        This account type appears on the <strong><?php echo e($statements[$accountType->classification] ?? 'Unknown'); ?></strong>.
                    </p>

                    <h6 class="mb-3">Normal Balance</h6>
                    <p class="mb-4">
                        <?php
                            $normalBalances = [
                                'asset' => 'Debit',
                                'liability' => 'Credit',
                                'equity' => 'Credit',
                                'revenue' => 'Credit',
                                'expense' => 'Debit'
                            ];
                        ?>
                        The normal balance for this account type is <strong><?php echo e($normalBalances[$accountType->classification] ?? 'Unknown'); ?></strong>.
                    </p>

                    <div class="mt-4">
                        <a href="#accountCategories" class="btn btn-soft-primary btn-sm mb-2">
                            <i class="bi bi-folder me-1"></i> View Categories
                        </a>
                        <a href="#accounts" class="btn btn-soft-info btn-sm mb-2">
                            <i class="bi bi-journal-bookmark me-1"></i> View Accounts
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\AccountCategory::class)): ?>
    <div class="card mb-4" id="accountCategories">
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">
                        <i class="bi bi-folder me-2"></i>Account Categories
                    </h4>
                </div>
                <div class="col-auto">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\AccountCategory::class)): ?>
                    <a href="<?php echo e(route('account-categories.create')); ?>" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-plus-circle me-1"></i> Add Category
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php
if (! isset($_instance)) {
    $html = \Livewire\Livewire::mount('account-type-account-categories-detail', ['accountType' => $accountType])->html();
} elseif ($_instance->childHasBeenRendered('T9Eue9w')) {
    $componentId = $_instance->getRenderedChildComponentId('T9Eue9w');
    $componentTag = $_instance->getRenderedChildComponentTagName('T9Eue9w');
    $html = \Livewire\Livewire::dummyMount($componentId, $componentTag);
    $_instance->preserveRenderedChild('T9Eue9w');
} else {
    $response = \Livewire\Livewire::mount('account-type-account-categories-detail', ['accountType' => $accountType]);
    $html = $response->html();
    $_instance->logRenderedChild('T9Eue9w', $response->id(), \Livewire\Livewire::getRootElementTagName($html));
}
echo $html;
?>
        </div>
    </div>
    <?php endif; ?>

    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Account::class)): ?>
    <div class="card mb-4" id="accounts">
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">
                        <i class="bi bi-journal-bookmark me-2"></i>Accounts
                    </h4>
                </div>
                <div class="col-auto">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Account::class)): ?>
                    <a href="<?php echo e(route('accounts.create')); ?>" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-plus-circle me-1"></i> Add Account
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php
if (! isset($_instance)) {
    $html = \Livewire\Livewire::mount('account-type-accounts-detail', ['accountType' => $accountType])->html();
} elseif ($_instance->childHasBeenRendered('60IIr6g')) {
    $componentId = $_instance->getRenderedChildComponentId('60IIr6g');
    $componentTag = $_instance->getRenderedChildComponentTagName('60IIr6g');
    $html = \Livewire\Livewire::dummyMount($componentId, $componentTag);
    $_instance->preserveRenderedChild('60IIr6g');
} else {
    $response = \Livewire\Livewire::mount('account-type-accounts-detail', ['accountType' => $accountType]);
    $html = $response->html();
    $_instance->logRenderedChild('60IIr6g', $response->id(), \Livewire\Livewire::getRootElementTagName($html));
}
echo $html;
?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Action Buttons -->
    <div class="d-flex justify-content-between mt-2 mb-4">
        <div>
            <a href="<?php echo e(route('account-types.index')); ?>" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left me-1"></i> Back to Account Types
            </a>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\AccountType::class)): ?>
            <a href="<?php echo e(route('account-types.create')); ?>" class="btn btn-outline-primary">
                <i class="bi bi-plus-circle me-1"></i> Create New Account Type
            </a>
            <?php endif; ?>
        </div>

        <div>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $accountType)): ?>
            <a href="<?php echo e(route('account-types.edit', $accountType)); ?>" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> Edit
            </a>
            <?php endif; ?>
            
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $accountType)): ?>
            <form action="<?php echo e(route('account-types.destroy', $accountType)); ?>" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this account type?')">
                <?php echo csrf_field(); ?>
                <?php echo method_field('DELETE'); ?>
                <button type="submit" class="btn btn-danger" <?php echo e($accountType->is_system ? 'disabled' : ''); ?>>
                    <i class="bi bi-trash me-1"></i> Delete
                </button>
            </form>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Acounting\github\accounting\resources\views/app/account_types/show.blade.php ENDPATH**/ ?>