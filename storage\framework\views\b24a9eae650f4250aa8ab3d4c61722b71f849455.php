

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707 = $component; } ?>
<?php $component = App\View\Components\PageHeader::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\PageHeader::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('icon', null, []); ?> folder-plus <?php $__env->endSlot(); ?>
         <?php $__env->slot('title', null, []); ?> Create New Account Category <?php $__env->endSlot(); ?>
         <?php $__env->slot('subtitle', null, []); ?> Add a new category to the chart of accounts <?php $__env->endSlot(); ?>
         <?php $__env->slot('breadcrumbs', null, []); ?> 
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Accounting</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('account-categories.index')); ?>">Account Categories</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create</li>
         <?php $__env->endSlot(); ?>
         <?php $__env->slot('controls', null, []); ?> 
            <a href="<?php echo e(route('account-categories.index')); ?>" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Categories
            </a>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707)): ?>
<?php $component = $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707; ?>
<?php unset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707); ?>
<?php endif; ?>
    <!-- End Page Header -->

    <!-- Form Card -->
    <form method="POST" action="<?php echo e(route('account-categories.store')); ?>">
        <?php echo csrf_field(); ?>
        
        <?php echo $__env->make('app.account_categories.form-inputs', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <div class="d-flex justify-content-end mt-4 border-top pt-4">
            <a href="<?php echo e(route('account-categories.index')); ?>" class="btn btn-outline-secondary me-2">
                <i class="bi bi-x-circle me-1"></i> Cancel
            </a>

            <button type="submit" class="btn btn-primary">
                <i class="bi bi-check-circle me-1"></i> Create Category
            </button>
        </div>
    </form>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Acounting\github\accounting\resources\views/app/account_categories/create.blade.php ENDPATH**/ ?>