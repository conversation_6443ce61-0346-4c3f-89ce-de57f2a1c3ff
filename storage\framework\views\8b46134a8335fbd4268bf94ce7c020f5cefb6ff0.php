<?php $editing = isset($accountType) ?>

<div class="row">
    <!-- Basic Information Card -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-header-title">
                    <i class="bi bi-info-circle me-2"></i>Basic Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-12 col-lg-6 mb-4">
                        <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.inputs.text','data' => ['name' => 'name','label' => 'Account Type Name','value' => old('name', ($editing ? $accountType->name : '')),'maxlength' => '255','placeholder' => 'Enter account type name','required' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('inputs.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'name','label' => 'Account Type Name','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('name', ($editing ? $accountType->name : ''))),'maxlength' => '255','placeholder' => 'Enter account type name','required' => true]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                    </div>
                    
                    <div class="col-sm-12 col-lg-6 mb-4">
                        <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.inputs.text','data' => ['name' => 'code','label' => 'Account Type Code','value' => old('code', ($editing ? $accountType->code : '')),'maxlength' => '255','placeholder' => 'Enter account type code','required' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('inputs.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'code','label' => 'Account Type Code','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('code', ($editing ? $accountType->code : ''))),'maxlength' => '255','placeholder' => 'Enter account type code','required' => true]); ?>
                            <div slot="hint">
                                <small class="form-text text-muted">
                                    A unique code for this account type (e.g., ASSET, LIAB, EQ, REV, EXP)
                                </small>
                            </div>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Classification Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-header-title">
                    <i class="bi bi-tag me-2"></i>Classification
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <label for="classification" class="form-label">Financial Statement Classification</label>
                    <div class="tom-select-custom">
                        <select name="classification" id="classification" required class="js-select form-select" data-hs-tom-select-options='{
                                    "placeholder": "Select a classification..."
                                  }'>
                            <?php $selected = old('classification', ($editing ? $accountType->classification : '')) ?>
                            <option value="asset" <?php echo e($selected == 'asset' ? 'selected' : ''); ?>>Asset</option>
                            <option value="liability" <?php echo e($selected == 'liability' ? 'selected' : ''); ?>>Liability</option>
                            <option value="equity" <?php echo e($selected == 'equity' ? 'selected' : ''); ?>>Equity</option>
                            <option value="revenue" <?php echo e($selected == 'revenue' ? 'selected' : ''); ?>>Revenue</option>
                            <option value="expense" <?php echo e($selected == 'expense' ? 'selected' : ''); ?>>Expense</option>
                        </select>
                    </div>
                    <small class="form-text text-muted mt-2">
                        This determines where accounts of this type will appear in financial statements.
                    </small>
                </div>
                
                <div class="form-check form-switch mb-3">
                    <input 
                        type="checkbox"
                        name="is_active"
                        id="is_active"
                        class="form-check-input"
                        value="1"
                        <?php echo e($editing && $accountType->is_active ? 'checked' : ''); ?>

                    />
                    <label class="form-check-label" for="is_active">Active</label>
                    <div class="form-text text-muted">
                        Inactive account types cannot be used for new accounts.
                    </div>
                </div>
                
                <?php if($editing): ?>
                <div class="form-check form-switch">
                    <input 
                        type="checkbox"
                        name="is_system"
                        id="is_system"
                        class="form-check-input"
                        value="1"
                        <?php echo e($editing && $accountType->is_system ? 'checked' : ''); ?>

                        <?php echo e($editing && $accountType->is_system ? 'disabled' : ''); ?>

                    />
                    <label class="form-check-label" for="is_system">System Account Type</label>
                    <div class="form-text text-muted">
                        System account types cannot be deleted and are required for the accounting system.
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Description Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-header-title">
                    <i class="bi bi-file-text me-2"></i>Description
                </h5>
            </div>
            <div class="card-body">
                <div>
                    <label for="description" class="form-label">Account Type Description</label>
                    <textarea 
                        name="description" 
                        id="description" 
                        class="form-control" 
                        rows="5" 
                        placeholder="Enter a description for this account type"
                    ><?php echo e(old('description', ($editing ? $accountType->description : ''))); ?></textarea>
                    <small class="form-text text-muted mt-2">
                        Provide a clear description of what this account type is used for and any special considerations.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\Acounting\github\accounting\resources\views/app/account_types/form-inputs.blade.php ENDPATH**/ ?>